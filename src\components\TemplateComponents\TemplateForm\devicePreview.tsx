import React, { useEffect } from "react";
import { Box, Typography, IconButton } from "@mui/material";
import VideoCallIcon from "@mui/icons-material/VideoCall";
import PhoneIcon from "@mui/icons-material/Phone";
import TemplatePreviewLayout from "./templatePreviewLayout";
import DevicePreviewCarousel from "./devicePreviewCarousel";
import { useAppSelector } from "../../../utils/redux-hooks";

// import { ChevronLeft, ChevronRight } from "lucide-react";

interface DevicePreviewProps {
  canEdit?: boolean;
  header?: string;
  body?: string;
  footer?: string;
  mediaType?: number;
  mediaFile?: any;
  buttons: {
    buttonType: string;
    buttonValue: string;
    buttonName?: string; // Optional property
  }[];
  style: any;
  carouselCards?: any;
  currentIndex?: number;
  setCurrentIndex?: any;
}

const DevicePreviewComponent: React.FC<DevicePreviewProps> = ({
  header,
  body,
  footer,
  mediaType,
  mediaFile,
  buttons,
  style,
  carouselCards,
  currentIndex = 0,
  setCurrentIndex,
}) => {
  const companyData = useAppSelector((state: any) => state?.companyData?.data);

  // No need for navigation functions as they're now in DevicePreviewCarousel

  useEffect(() => {
    // Only update the currentIndex if it's out of bounds
    if (currentIndex >= carouselCards?.length && setCurrentIndex) {
      setCurrentIndex(Math.max(0, carouselCards?.length - 1));
    }
  }, [carouselCards?.length, currentIndex, setCurrentIndex]);

  return (
    <Box
      sx={{
        mt: 8,
        backgroundImage: `url("/images/whatsappBG.jpg")`,
        height: "100%",
        display: "flex",
        minHeight: "500px",
        overflowX: "hidden",
        flexDirection: "column",
        borderRadius: "16px",
        width: "100%",
        maxWidth: style?.maxWidth ? style.maxWidth : "380px",
      }}
    >
      <Box
        style={{
          background: "#f0f0f0",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "10px 20px 0 20px",
          borderTopLeftRadius: "16px",
          borderTopRightRadius: "16px",
        }}
      >
        {/* Left side */}
        <Box style={{ display: "flex", alignItems: "center" }}>
          <Typography variant="body2">22:30</Typography>
        </Box>

        {/* Right side */}
        <Box style={{ display: "flex", alignItems: "center" }}>
          <Typography variant="body2">5G</Typography>
          <Typography variant="body2" sx={{ marginLeft: "8px" }}>
            80%
          </Typography>
        </Box>
      </Box>

      <Box
        style={{
          background: "#f0f0f0",
          padding: "0 10px",
          display: "flex",
          alignItems: "center",
        }}
      >
        <img
          src="/images/profile.png" // Sample profile pic
          alt="Profile"
          style={{ width: "30px", height: "30px", borderRadius: "50%" }}
        />
        <Typography ml={1} sx={{ fontSize: 14, fontWeight: "bold" }}>
          {companyData?.company?.businessName}
        </Typography>
        {/* Phone and video call icons */}
        <Box style={{ marginLeft: "auto" }}>
          <IconButton aria-label="video call">
            <VideoCallIcon />
          </IconButton>
          <IconButton aria-label="phone call">
            <PhoneIcon />
          </IconButton>
        </Box>
      </Box>
      <Box
        style={{
          background: "#fff",
          borderRadius: "10px",
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
          // overflow: "scroll",
          height: "auto",
          margin: "8px 10px 4px 10px",
        }}
      >
        <TemplatePreviewLayout
          header={header}
          body={body}
          footer={footer}
          mediaType={mediaType}
          mediaFile={mediaFile}
          buttons={buttons}
          carouselCards={carouselCards}
        />
      </Box>
      {mediaType === 6 && carouselCards && carouselCards?.length > 0 && (
        <DevicePreviewCarousel
          carouselCards={carouselCards}
          currentIndex={currentIndex || 0}
          setCurrentIndex={setCurrentIndex}
        />
      )}

      {/* <Box
        m={2}
        style={{
          background: "#ffffff",
          border: "1px solid #999",
          display: "flex",
          alignItems: "center",
          borderRadius: "10px",
          marginTop: "auto",
        }}
      >
        <Box style={{ flex: 1 }}></Box>

        <IconButton
          style={{ marginRight: "4px", color: "#075e54" }}
          aria-label="send"
        >
          <SendIcon />
        </IconButton>
      </Box> */}
    </Box>
  );
};

export default DevicePreviewComponent;
