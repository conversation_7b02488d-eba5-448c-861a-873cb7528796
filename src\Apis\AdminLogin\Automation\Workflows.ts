/* global process */

import axios from "axios";

const WORKFLOW_API_URL = process.env.REACT_APP_BASE_URL;

const WORKFLOW_REACTFLOW_API_URL = process.env.REACT_APP_WEB_SOCKET_BASE_URL;

const getWorkflowList = (companyId: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/GetWorkflowList?companyId=${companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const getWorkflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/GetWorkflow?companyId=${data?.companyId}&workflowName=${data?.workflowName}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const getWorkflowNames = (companyId: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/GetWorkflowNames/${companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const getVariableName = (companyId: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/GetVeriableNames?companyId=${companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const deleteWorkflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/DeleteWorkflow?companyId=${data?.companyId}&workflowName=${data?.workflowName}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const createWorkflowCustomMessage = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AddWorkflowList`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data,
  });
};

const createWorkflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/CreateWorkflow/${data?.isNew}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data.workflowData,
  });
};

const updateWorkflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/UpdateWorkflow`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data.workflowData,
  });
};

const updateWorkflowList = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/updateWorkflowList`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data,
  });
};

const downloadWorkflowResponseListByName = (data: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/DownloadWorkflowResponseListByName?companyId=${data?.companyId}&workflowName=${data?.workflowName}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    responseType: "blob",
  });
};

// select a response

const getSelectResponseData = (payload: any) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AutomationSelectResponse/${payload}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const createSelectResponse = (companyId: string, response: string) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AutomationSelectResponse/${companyId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: response,
  });
};

const updateSelectResponse = (selectResponseId: string, response: string) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AutomationSelectResponse/${selectResponseId}`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: response,
  });
};

const deleteSelectResponse = (selectResponseId: string) => {
  return axios({
    url: `${WORKFLOW_API_URL}/AutoReplyMessage/AutomationSelectResponse/${selectResponseId}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

// Workflow Rectflow apis
const createWorkflowReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data,
  });
};
const updateWorkflowReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/${data?.id}`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data?.data,
  });
};

const getAllWorkflowsReactflow = (companyId: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/${companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};
const deleteWorkflowReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/${data?.companyId}/${data?.WorkflowId}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const getWorkflowReactflowById = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/GetWorkflowById/${data}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const createKeywordReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/AddKeyword`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data,
  });
};

const deleteKeywordReactflow = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/DeleteKeywords/${data?.workflowId}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data?.keywords,
  });
};

const getSourcesAndSubSources = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/GetAllSubSourcesBySource?BusinessId=${data.businessId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const getWorkflowAllKeywords = (id: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/GetAllKeywords/${id}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const AddSaveResponseAttribute = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/AttributeName`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data,
  });
};

const getSaveResponseAttribute = () => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/AttributeName`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

const saveWorkflowCustomResponse = (payload: {
  businessId: string;
  nodeId: string;
  userId: string;
  attributeId: string;
}) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/WorkflowCustomResponse`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: payload,
  });
};

const getKeywords = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/GetKeywords/${data?.companyId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};
const toggleWorkflowActive = (data: any) => {
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/ToggleActive/${data?.workflowId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
    data: data,
  });
};
const getFlowstartNodes = (data: any) => {
  console.log("data", data);
  return axios({
    url: `${WORKFLOW_REACTFLOW_API_URL}/api/workflow/GetFlowstartNodes/${data?.businessId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: localStorage.getItem("token"),
    },
  });
};

export const WORKFLOW_API = {
  getWorkflowList,
  getVariableName,
  deleteWorkflow,
  createWorkflowCustomMessage,
  updateWorkflowList,
  createWorkflow,
  getWorkflowNames,
  getSelectResponseData,
  getWorkflow,
  updateWorkflow,
  downloadWorkflowResponseListByName,
  createSelectResponse,
  updateSelectResponse,
  deleteSelectResponse,
  // workflow Reactflow
  createWorkflowReactflow,
  updateWorkflowReactflow,
  getAllWorkflowsReactflow,
  deleteWorkflowReactflow,
  getWorkflowReactflowById,
  createKeywordReactflow,
  deleteKeywordReactflow,
  getSourcesAndSubSources,
  getWorkflowAllKeywords,
  AddSaveResponseAttribute,
  getSaveResponseAttribute,
  saveWorkflowCustomResponse,
  getKeywords,
  toggleWorkflowActive,
  getFlowstartNodes,
};
