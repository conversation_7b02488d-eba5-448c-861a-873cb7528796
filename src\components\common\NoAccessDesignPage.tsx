import { Box, Typography, Button, IconButton } from "@mui/material";
import ArrowRightIconSvg from "../../assets/svgs/ArrowRightIconSvg";
import Bag from "../../assets/NoAccessDesignPage/svgs/Bag";
import Message32 from "../../assets/NoAccessDesignPage/svgs/Message32";
import LikeCount from "../../assets/NoAccessDesignPage/svgs/LikeCount";
import Smile from "../../assets/NoAccessDesignPage/svgs/Smile";
import Rupees from "../../assets/NoAccessDesignPage/svgs/Rupees";
import Light from "../../assets/NoAccessDesignPage/svgs/Light";
import Message10 from "../../assets/NoAccessDesignPage/svgs/Message10";
import PieChart from "../../assets/NoAccessDesignPage/svgs/PieChart";
import Whatsapp from "../../assets/NoAccessDesignPage/svgs/Whatsapp";
import CheckCircle from "../../assets/NoAccessDesignPage/svgs/CheckCircle";
import Like from "../../assets/NoAccessDesignPage/svgs/Like";
import Building from "../../assets/NoAccessDesignPage/svgs/Building";
import Heart from "../../assets/NoAccessDesignPage/svgs/Heart";
import StarCount from "../../assets/NoAccessDesignPage/svgs/StarCount";
import Sparkle from "../../assets/NoAccessDesignPage/svgs/Sparkle";
import WhatsappIcon from "../../assets/NoAccessDesignPage/svgs/WhatsappIcon";
import EngagetoLogoSvg from "../../assets/NoAccessDesignPage/svgs/EngagetoSvg";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { SendEmail } from "../../redux/slices/Utility/NoAccessDesignPage";
import Player from "lottie-react";
import animationData from "../../assets/animations/rotate.json";
import { useState } from "react";

const circles = [
  {
    style: { width: "450px", height: "450px" },
    images: [
      {
        src: <Bag />,
        style: { top: "0", left: "50%" },
      },
      {
        src: <Message32 />,
        style: { top: "35%", left: "100%" },
      },
      {
        src: <LikeCount />,
        style: { top: "100%", left: "50%" },
      },
    ],
  },
  {
    style: { width: "750px", height: "750px" },
    images: [
      {
        src: <Smile />,
        style: { top: "6%", left: "74%" },
      },
      {
        src: <Rupees />,
        style: { top: "50%", left: "100%" },
      },
      {
        src: <Light />,
        style: { top: "94%", left: "73%" },
      },
      {
        src: <Bag />,
        style: { top: "80%", left: "10%" },
      },
      {
        src: <Message10 />,
        style: { top: "55%", left: "1%" },
      },
      {
        src: <PieChart />,
        style: { top: "22%", left: "9%" },
      },
    ],
  },
  {
    style: { width: "1000px", height: "1000px" },
    images: [
      {
        src: <Whatsapp />,
        style: { top: "20%", left: "90%" },
      },
      {
        src: <CheckCircle />,
        style: { top: "44%", left: "100%" },
      },
      {
        src: <Like />,
        style: { top: "79%", left: "91%" },
      },
      {
        src: <CheckCircle />,
        style: { top: "90%", left: "80%" },
      },
      {
        src: <Building />,
        style: { top: "87%", left: "16%" },
      },
      {
        src: <Heart />,
        style: { top: "57%", left: "0%" },
      },
      {
        src: <StarCount />,
        style: { top: "25%", left: "7%" },
      },
    ],
  },
  {
    style: { width: "1250px", height: "1250px" },
    images: [
      {
        src: <Sparkle />,
        style: { top: "21%", left: "91%" },
      },
      {
        src: <Bag />,
        style: { top: "50%", left: "100%" },
      },
      {
        src: <PieChart />,
        style: { top: "69%", left: "96%" },
      },
      {
        src: <Rupees />,
        style: { top: "82%", left: "12%" },
      },
      {
        src: <Smile />,
        style: { top: "67%", left: "3%" },
      },
      {
        src: <CheckCircle />,
        style: { top: "33%", left: "3%" },
      },
    ],
  },
  {
    style: { width: "1500px", height: "1500px" },
    images: [
      {
        src: <Heart />,
        style: { top: "50%", left: "100%" },
      },
      {
        src: <Bag />,
        style: { top: "50%", left: "0%" },
      },
    ],
  },
  {
    style: { width: "1750px", height: "1750px" },
    images: [
      {
        src: <WhatsappIcon />,
        style: { top: "40%", left: "99%" },
      },
      {
        src: <StarCount />,
        style: { top: "70%", left: "96%" },
      },
      {
        src: <Sparkle />,
        style: { top: "67%", left: "3%" },
      },
      {
        src: <Heart />,
        style: { top: "30%", left: "4%" },
      },
    ],
  },
];

const NoAccessDesignPage = ({ emailContent }: any) => {
  const dispatch = useAppDispatch();
  const tenantId = useAppSelector((state: any) => state.adminLogin?.tenantId);
  const [isMailSent, setIsMailSent] = useState(false);

  const joinEngageTo = async () => {
    const data = {
      Sender: "<EMAIL>",
      Subject: "New Enquiry for EngageTo",
      ContentBody: emailContent || `${tenantId} has interest in engageto`,
      Attachments: [],
      ToRecipients: [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ],
      // CcRecipients: ["<EMAIL>"],
      // BccRecipients: ["<EMAIL>"],
    };
    const response = await dispatch(SendEmail(data));
    if (response?.payload?.succeeded) {
      setIsMailSent(true);
    }
  };
  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      sx={{
        height: "100vh",
        backgroundColor: "white",
        overflow: "hidden",
      }}
    >
      <Box position="relative" width="100%" height="100%">
        {/* Lottie Animation */}
        <Box position="absolute" width="100%">
          <Player autoplay loop animationData={animationData} />
        </Box>

        {/* Circles with Images */}
        {circles.map((circle: any, i: number) => (
          <div
            key={i}
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              borderRadius: "50%",
              transform: "translate(-50%, -50%)",
              border: "1px solid #E4E4E4",
              borderColor: "grey.900",
              ...circle.style,
            }}
          >
            {circle.images.map((icon: any, j: number) => (
              <IconButton
                sx={{
                  ...icon.style,
                  position: "absolute",
                  transform: "translate(-50%, -50%)",
                }}
              >
                {icon.src}
              </IconButton>
            ))}
          </div>
        ))}

        {/* Main Content */}
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          position="absolute"
          width="100%"
          height="100%"
          fontFamily="Poppins"
        >
          <EngagetoLogoSvg />

          <Typography
            variant="h4"
            fontWeight={900}
            mt={3}
            fontSize={"40px"}
            color="#10100E"
            lineHeight={"45px"}
          >
            Engage
          </Typography>
          <Typography
            variant="h4"
            fontWeight={900}
            fontSize={"40px"}
            color="#10100E"
            lineHeight={"45px"}
          >
            Clients Smarter
          </Typography>
          <Typography variant="subtitle1" color="text.secondary" mt={1}>
            Bring the power of next gen WhatsApp
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Business API to Leadrat CRM
          </Typography>
          {isMailSent && (
            <>
              <div style={{ color: "#00934f", marginTop: "10px" }}>
                Thank you for your interest🎉
              </div>
              <div style={{ color: "#00934f" }}>
                Our EngageTo Team will get back to you.
              </div>
            </>
          )}
          {!isMailSent && (
            <button
              // variant="contained"
              // color="primary"
              style={{
                color: "white",
                backgroundColor: "black",
                borderRadius: "50px",
                padding: "13px 15px ",
                //   paddingY: "10px",
                margin: "32px 0 0 0 ",
                cursor: "pointer",
              }}
              onClick={joinEngageTo}
            >
              <div
                style={{
                  fontWeight: 500,
                  fontSize: "20px",
                  letterSpacing: "1.3px",
                }}
              >
                Join EngageTo now
              </div>
            </button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default NoAccessDesignPage;
