import {
  Autocomplete,
  Box,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { DraftEditorComponent } from "../../common/DraftEditorComponent";
import { ContentState, convertToRaw, EditorState } from "draft-js";
import { reactDraftWysiwygToolbarOptionsarticle } from "../../../utils/react-draft-wysiwyg-options";
import {
  extractVariables,
  getNextVariableCount,
  insertTextAtCursor,
} from "./functions";
import TextFieldWithBorderComponent from "../../common/TextFieldWithBorderComponent";
import DeleteIconSvg from "../../../assets/svgs/DeleteIconSvg";
import SelectListDialog from "./SelectListDialog";
import { bgColors } from "../../../utils/bgColors";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import LoadingComponent from "../../common/LoadingComponent";
import { ButtonListType } from "./SendMessageNode";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import AddVariablePopup from "./AddVariablePopup";
import { v4 as uuidv4 } from "uuid";
import { updateWorkflowList } from "../../../redux/slices/Workflows/updateWorkflowListSlice";
import { toastActions } from "../../../utils/toastSlice";
import { getWorkflowList } from "../../../redux/slices/Workflows/getWorkflowListSlice";
import AddSelectResponse from "./AddSelectResponsePopup";
import { getSelectResponse } from "../../../redux/slices/Workflows/getSelectResponse";
import DeletePopUp from "../../common/DeletePopup";
import { deleteSelectResponse } from "../../../redux/slices/Workflows/deleteSelectResponseSlice";
import { parseTextToDraft } from "../../../utils/functions";
import { InteractiveType } from "./enums";
import { NodeType } from "./enums";
import VariableAssignmentComponentReactflow from "./variableAssignmentComponentReactflow";

export const ButtonOptions = [
  { value: InteractiveType.None, label: "None" },
  { value: InteractiveType.Button, label: "Button" },
  { value: InteractiveType.List, label: "List" },
];
const VariableAttributesData = [
  { name: "Name", value: "Name" },
  { name: "Contact", value: "Contact" },
  { name: "CountryCode", value: "CountryCode" },
  { name: "CountryName", value: "CountryName" },
  { name: "Email", value: "Email" },
  { name: "LeadStatus", value: "LeadStatus" },
  { name: "LeadSubStatus", value: "LeadSubStatus" },
  { name: "ScheduledAt", value: "ScheduledAt" },
  { name: "Project", value: "Project" },
];

const CustomResponseComp = ({
  selectedButtonValue,
  handleSelectedButtonValue,
  buttons,
  handleButtons,
  updateNodeData,
  selectedList,
  handleSelectedList,
  message,
  handleMessage,
  handleButtonText,
  handleRemoveButton,
  selectedAction,
  variables,
  handleVariablesChange,
  handleVariablesValueChange,
  handleVariablesFallbackValueChange,
  response,
  editSelectResponseId,
  handleSelectedResponse,
  handleResponse,
  handleEditSelectResponseId,
  deleteSelectResponseId,
  handleEditorStateChange,
  editorState,
  setEditorState,
  selectedUserResponse,
}: any) => {
  const dispatch = useAppDispatch();
  const { workflowListData, workflowListStatus } = useAppSelector(
    (state: any) => state.getWorkflowList
  );
  const { selectResponseData } = useAppSelector(
    (state: any) => state?.getSelectResponseNames
  );

  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;
  const [errors, setErrors] = useState<boolean[]>([]);
  const [hasErrors, setHasErrors] = useState(false);
  const [openListDialog, setOpenListDialog] = useState(false);
  const [updateList, setUpdateList] = useState<ButtonListType | null>(null);
  const [isAddNewPopupOpen, setIsAddNewPopupOpen] = useState(false);
  const [openAddselectResponseDialog, setOpenAddSelectResponseDialog] =
    useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isWorkflowDeleteLoading, setIsWorkflowDeleteLoading] = useState(false);
  const [isWorkflowEditing, setIsWorkflowEditing] = useState(false);
  const [isDeletingVariable, setIsDeletingVariable] = useState(false);

  // Local state for selected variable (persisted in this component)
  const [selectedVariable, setSelectedVariable] = useState(
    selectedUserResponse || null
  );

  useEffect(() => {
    setSelectedVariable(selectedUserResponse || null);
  }, [selectedUserResponse]);

  // Get data from Redux if not passed as prop
  const data = useAppSelector(
    (state: any) => state.getSaveResponseAttribute.getSaveResponseAttributeData
  );
  const attributesData = data?.data;

  const handleButtonChange = (e: any) => {
    const value = e.target.value;
    handleSelectedButtonValue(value);

    // Update the selectedAction value when changing button type
    if (selectedAction) {
      const updatedAction = {
        ...selectedAction,
        id:
          value === InteractiveType.Button
            ? 2
            : value === InteractiveType.List
            ? 3
            : 1,
        value: value,
        // Update label and description based on button type
        label:
          value === InteractiveType.Button
            ? "Text Button"
            : value === InteractiveType.List
            ? "List Button"
            : "Send a Message",
        description:
          value === InteractiveType.Button
            ? "Add a text button"
            : value === InteractiveType.List
            ? "Create a list menu"
            : "Send a WhatsApp message",
      };
      updateNodeData((data: any) => ({
        ...data,
        selectedAction: updatedAction,
        selectedButtonValue: value,
        // Initialize buttons array when switching to button type
        buttons:
          value === InteractiveType.Button
            ? [
                {
                  id: uuidv4(),
                  text: "",
                },
              ]
            : [],
        // Set selectedList to null when changing from list type
        selectedList: value !== InteractiveType.List ? null : data.selectedList,
      }));
    }
  };

  const handleAddButton = () => {
    if (buttons.length < 3) {
      handleButtons({
        id: uuidv4(),
        text: "",
      });
    }
  };

  const handleButtonTextChange = (id: string, value: string) => {
    // Validate button text length
    const isValid = value.length <= 20;
    const buttonIndex = buttons.findIndex((button: any) => button.id === id);

    if (buttonIndex !== -1) {
      const newErrors = [...errors];
      newErrors[buttonIndex] = !isValid;
      setErrors(newErrors);
      setHasErrors(newErrors.some((error) => error));
    }

    handleButtonText(id, value);
  };

  const handleCloseSelectResponse = () => {
    setOpenAddSelectResponseDialog(false);
    handleEditSelectResponseId(null);
    handleResponse("");
  };

  useEffect(() => {
    if (selectedButtonValue === "button" && !buttons.length) {
      handleButtons({
        id: uuidv4(),
        text: "",
      });
    }
  }, [selectedButtonValue]);

  const handleOnListSelect = (listData: any) => {
    handleSelectedList(listData);
    setOpenListDialog(false);
  };

  const fetchWorkflowList = async () => {
    const payload = {
      companyId: userData?.companyId,
    };

    try {
      const result = await dispatch(getWorkflowList(payload)).unwrap();
      if (result.success) {
        // dispatch(
        //   toastActions.setToaster({
        //     message: result.message,
        //     type: "success",
        //   })
        // );
      } else {
        // dispatch(
        //   toastActions.setToaster({
        //     message: result.message || "Failed to fetch workflow list",
        //     type: "error",
        //   })
        // );
      }
    } catch (error) {
      // dispatch(
      //   toastActions.setToaster({
      //     message: "An error occurred while fetching the workflow list",
      //     type: "error",
      //   })
      // );
    }
  };

  const handleListUpdate = async (listData: any) => {
    const payload = {
      id: listData.id,
      companyId: userData?.companyId,
      listName: listData?.listName,
      buttonName: listData.buttonName,
      inputs: listData.inputs,
    };

    try {
      const result = await dispatch(updateWorkflowList(payload)).unwrap();

      if (result.success) {
        fetchWorkflowList();
        dispatch(
          toastActions.setToaster({
            message: "Workflow list updated successfully",
            type: "success",
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            message: result.message || "Failed to update workflow list",
            type: "error",
          })
        );
      }
    } catch (error) {
      dispatch(
        toastActions.setToaster({
          message: "An error occurred while updating the workflow list",
          type: "error",
        })
      );
    }
  };

  const handleSelectedListChange = (stepNumber: number, list: any) => {
    setUpdateList(list);
  };

  const handleAddNewClick = () => {
    setIsAddNewPopupOpen(true);
  };

  const handlePopupClose = () => {
    setIsAddNewPopupOpen(false);
  };

  const handlePopupSuccess = () => {};

  const rearrangeVariableIndexes = (variables: any[]) => {
    return variables.map((variable, index) => ({
      ...variable,
      index: index,
    }));
  };

  const handleRemoveVariable = (index: number) => {
    setIsDeletingVariable(true);

    // Get the variable to be removed
    const variableToRemove = variables?.customMessageVariables?.[index];

    if (!variableToRemove) return;

    // Filter out the variable at the specified index
    const updatedVariables = variables?.customMessageVariables?.filter(
      (_: any, i: number) => i !== index
    );

    // Rearrange indexes of remaining variables
    const reindexedVariables = rearrangeVariableIndexes(updatedVariables);

    // Update the variables state with reindexed variables
    handleVariablesChange(reindexedVariables);

    // Remove the variable from the editor state
    const content = editorState.getCurrentContent();
    if (variableToRemove.veriable) {
      const newContent = content
        .getPlainText()
        .replace(variableToRemove.veriable, "");
      const newEditorState = EditorState.createWithContent(
        ContentState.createFromText(newContent)
      );
      setEditorState(newEditorState);
      handleEditorStateChange(newEditorState);
    }

    // Reset the deletion flag after a short delay
    setTimeout(() => {
      setIsDeletingVariable(false);
    }, 100);
  };

  const handleAddVariable = () => {
    const nextCount = getNextVariableCount(editorState, "editor");
    const variableText = `{{${nextCount}}}`;
    const newEditorState = insertTextAtCursor(editorState, variableText);
    setEditorState(newEditorState);
    const currentVariables = variables?.customMessageVariables || [];
    const newVariable = {
      index: currentVariables.length, // Use current length as index
      veriable: variableText,
      value: "",
      type: 1,
      fallbackValue: "",
      referenceTableType: 0,
    };
    const updatedVariables = [...currentVariables, newVariable];
    handleVariablesChange(updatedVariables);
    handleEditorStateChange(newEditorState, "editor");
  };

  const handleDeleteConfirm = async () => {
    try {
      const payload = {
        selectResponseId: deleteSelectResponseId,
      };
      const result = await dispatch(deleteSelectResponse(payload)).unwrap();
      if (result.success) {
        handleSelectedResponse("");
        await dispatch(getSelectResponse({ companyId: userData?.companyId }));
        dispatch(
          toastActions.setToaster({
            message: "Workflow deleted successfully",
            type: "success",
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            message: result.message || "Failed to delete workflow",
            type: "error",
          })
        );
      }
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          message:
            error.message || "An error occurred while deleting the workflow",
          type: "error",
        })
      );
    }
    setIsDeleteConfirmOpen(false);
  };

  const handleDeleteCancel = () => {
    setIsDeleteConfirmOpen(false);
  };

  const handleResponseChange = async (event: any) => {
    if (typeof event === "string") {
      const payload = {
        companyId: userData?.companyId,
      };
      const result = await dispatch(getSelectResponse(payload));
      const selectedResponseObject = result?.payload?.find(
        (item: any) => item.selectResponse === event
      );
      handleSelectedResponse(event);
      handleMessage(
        selectedResponseObject ? selectedResponseObject.selectResponse : ""
      );
    } else {
      const value = event.target.value;
      handleSelectedResponse(value);
      const selectedResponseObject = selectResponseData.find(
        (item: any) => item.id === value
      );

      handleMessage(
        selectedResponseObject ? selectedResponseObject.selectResponse : ""
      );
    }
  };

  useEffect(() => {
    if (typeof handleMessage === "function" && !isDeletingVariable) {
      const plainText = editorState.getCurrentContent().getPlainText();
      handleMessage(plainText);

      // Get current variables from editor content
      const extractedVariables = extractVariables(
        editorState,
        variables?.customMessageVariables || []
      );
      const currentVariables = variables?.customMessageVariables || [];

      // Filter out variables that no longer exist in the editor content
      const updatedVariables = currentVariables.filter((variable: any) =>
        plainText.includes(variable.veriable)
      );

      // Add any new variables that were found
      extractedVariables.forEach((variable: any) => {
        if (
          !updatedVariables.some((v: any) => v.veriable === variable.veriable)
        ) {
          updatedVariables.push({
            ...variable,
            index: updatedVariables.length,
            type: 1,
            referenceTableType: 0,
          });
        }
      });

      // Update variables if there are changes
      if (
        JSON.stringify(updatedVariables) !== JSON.stringify(currentVariables)
      ) {
        handleVariablesChange(updatedVariables);
      }
    }
  }, [editorState, isDeletingVariable]);

  const prevEditorState = useRef<EditorState | null>(null);

  useEffect(() => {
    if (!handleSelectedResponse) return;

    const selectedItem = selectResponseData?.find(
      (item: any) => item?.selectResponse === message
    );

    if (selectedItem) {
      handleSelectedResponse(selectedItem?.id);
    }

    const currentText = editorState.getCurrentContent().getPlainText();
    if (message && currentText !== message) {
      const newState = EditorState.createWithContent(parseTextToDraft(message));
      if (prevEditorState.current !== newState) {
        setEditorState(newState);
        prevEditorState.current = newState;
      }
    } else if (!message && editorState.getCurrentContent().hasText()) {
      setEditorState(EditorState.createEmpty());
    }
  }, [selectResponseData, message, editorState]);

  // Add useEffect to initialize button when component mounts with InteractiveType.Button
  useEffect(() => {
    if (
      selectedAction?.nodeType === NodeType.InteractiveMessage &&
      selectedAction?.value === InteractiveType.Button &&
      (!selectedButtonValue || selectedButtonValue === InteractiveType.None)
    ) {
      handleSelectedButtonValue(InteractiveType.Button);
      if (!buttons || buttons.length === 0) {
        handleButtons({
          id: uuidv4(),
          text: "",
        });
      }
    }
  }, [selectedAction]);

  useEffect(() => {
    fetchWorkflowList();
  }, []);

  // Handler for Save User Response selection
  const handleSaveUserResponseChange = async (_: any, newValue: any) => {
    if (newValue?.id === "add-new") {
      handleAddNewClick();
    } else {
      setSelectedVariable(newValue);

      setTimeout(() => {
        updateNodeData((prev: any) => ({
          ...prev,
          selectedUserResponse: newValue,
        }));
      }, 0);
      // // Save to backend
      // if (newValue?.id) {
      //   const payload = {
      //     businessId: userData?.companyId,
      //     nodeId: workflowNodeId,
      //     userId: userData?.userId,
      //     attributeId: newValue.id,
      //   };
      //   const result = await dispatch(
      //     saveWorkflowCustomResponse(payload)
      //   );
      // }
    }
  };

  return (
    <Box
      sx={{
        height: "100%",
        overflow: "auto",
        display: "flex",
        flexDirection: "column",
        // gap: 2
      }}
    >
      <>
        <DraftEditorComponent
          editorState={editorState}
          handleEditorStateChange={(newEditorState: EditorState) => {
            setEditorState(newEditorState);
            handleEditorStateChange(newEditorState);
          }}
          reactDraftWysiwygToolbarOptionsarticle={
            reactDraftWysiwygToolbarOptionsarticle
          }
          handleAddVariable={handleAddVariable}
        />
        {variables?.customMessageVariables?.length > 0 &&
          variables?.customMessageVariables?.map(
            (item: any) => item.type === 1
          ) && (
            <VariableAssignmentComponentReactflow
              variables={variables}
              editorState={editorState}
              variableType={1}
              handleVariablesChange={handleVariablesChange}
              handleRemoveVariable={handleRemoveVariable}
              onVariablesValueChange={handleVariablesValueChange}
              onVariableFallbackValueChange={handleVariablesFallbackValueChange}
              attributesData={VariableAttributesData}
            />
          )}
      </>

      <AddSelectResponse
        open={openAddselectResponseDialog}
        handleClose={handleCloseSelectResponse}
        handleResponseChange={handleResponseChange}
        editSelectResponseId={editSelectResponseId}
        setResponse={handleResponse}
        response={response}
      />

      <Box>
        <Typography sx={{ fontSize: 14, mt: 2, mb: 2 }}>
          Add buttons or lists to your response
        </Typography>
        <Select
          fullWidth
          displayEmpty
          value={selectedButtonValue}
          onChange={handleButtonChange}
          sx={{
            mb: 2,
            height: "36px",
            fontSize: "14px",
            borderRadius: "10px",
            "& .MuiOutlinedInput-root": {
              borderRadius: "10px",
            },
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                width: "5%",
              },
            },
          }}
        >
          {ButtonOptions.map((option) => (
            <MenuItem
              key={option.value}
              sx={{ fontSize: 14 }}
              value={option.value}
            >
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {selectedButtonValue === InteractiveType.Button && (
          <Box>
            {buttons?.map(({ id, text }: any, index: number) => (
              <Box key={id}>
                <Typography sx={{ mr: 2, fontSize: "14px" }}>
                  Button {index + 1}
                </Typography>
                <Box
                  sx={{ display: "flex", alignItems: "center", mb: 2, mt: 1 }}
                >
                  <TextFieldWithBorderComponent
                    label={`Button text ${index + 1}`}
                    value={text}
                    name="buttonText"
                    onChange={(e) => handleButtonTextChange(id, e.target.value)}
                    placeholder={`Button text ${index + 1}`}
                    fullWidth
                    size="small"
                    error={errors[index]}
                    helperText={errors[index] ? "Max 20 char's allowed" : ""}
                  />
                  {buttons?.length > 1 && (
                    <IconButton
                      onClick={() => handleRemoveButton(id)}
                      size="small"
                      sx={{ ml: 1 }}
                    >
                      <DeleteIconSvg />
                    </IconButton>
                  )}
                </Box>
              </Box>
            ))}
            {buttons?.length < 3 && (
              <Typography
                onClick={handleAddButton}
                style={{
                  color: bgColors.green,
                  fontWeight: "600",
                  fontSize: "14px",
                  cursor: "pointer",
                }}
              >
                + Add another button
              </Typography>
            )}
          </Box>
        )}
        {selectedButtonValue === InteractiveType.List && (
          <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
            {workflowListStatus === "loading" ? (
              <LoadingComponent height="100%" color={bgColors.blue} />
            ) : (
              <Box
                sx={{
                  backgroundColor: bgColors.blue3,
                  padding: 1,
                  width: "100%",
                }}
              >
                <Box>
                  <Typography
                    onClick={() => setOpenListDialog(true)}
                    style={{
                      color: bgColors.green,
                      fontWeight: "600",
                      fontSize: "14px",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      gap: "10px",
                    }}
                  >
                    <span style={{ fontSize: "20px" }}>≡</span>
                    Select a List
                  </Typography>
                  <Typography pl={2}>
                    {selectedList?.listName || "No List is Selected"}
                  </Typography>
                </Box>
                <SelectListDialog
                  open={openListDialog}
                  onClose={() => setOpenListDialog(false)}
                  onSelect={handleOnListSelect}
                  onUpdate={handleListUpdate}
                  workflowListData={workflowListData}
                  handleSelectedListChange={handleSelectedListChange}
                />
              </Box>
            )}
          </Box>
        )}
        <Box sx={{ mt: 1, mb: 2 }}>
          <Box sx={{ backgroundColor: bgColors.blue3, padding: 1 }}>
            <Typography
              style={{
                color: bgColors.green,
                fontWeight: "600",
                fontSize: "14px",
                marginBottom: 8,
              }}
            >
              Save User Response
            </Typography>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                gap: 1,
              }}
            >
              <Autocomplete
                options={[
                  { id: "add-new", veriableName: "+ Add New" },
                  ...(attributesData?.length > 0 ? attributesData : []),
                ]}
                getOptionLabel={(option) => option.name}
                value={selectedVariable || null}
                onChange={handleSaveUserResponseChange}
                disablePortal
                disableClearable
                renderInput={(params) => {
                  // Add cross icon to clear selection using InputAdornment
                  return (
                    <TextField
                      {...params}
                      placeholder="Select Variable"
                      size="small"
                      sx={{
                        minHeight: 32,
                        fontSize: 14,
                        background: "#fff",
                        borderRadius: "8px",
                      }}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <InputAdornment position="end">
                            {selectedVariable && (
                              <CancelOutlinedIcon
                                fontSize="small"
                                color="error"
                                style={{ cursor: "pointer", marginRight: 4 }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedVariable(null);
                                  updateNodeData((prev: any) => ({
                                    ...prev,
                                    selectedUserResponse: null,
                                  }));
                                }}
                              />
                            )}
                            {params.InputProps.endAdornment}
                          </InputAdornment>
                        ),
                      }}
                    />
                  );
                }}
                renderOption={(props, option) => {
                  if (option.id === "add-new") {
                    return (
                      <li {...props} key={option.id}>
                        <span
                          style={{ color: bgColors.green, fontWeight: 700 }}
                        >
                          {option.veriableName}
                        </span>
                      </li>
                    );
                  }
                  return (
                    <li {...props} key={option.id}>
                      {option.name}
                    </li>
                  );
                }}
                ListboxProps={{
                  style: {
                    maxHeight: 200,
                    overflow: "auto",
                    zIndex: 1300,
                    width: "100%",
                  },
                }}
                sx={{ width: "100%" }}
              />
            </Box>
          </Box>
          <AddVariablePopup
            open={isAddNewPopupOpen}
            onClose={handlePopupClose}
            onSuccess={handlePopupSuccess}
          />
          <DeletePopUp
            open={isDeleteConfirmOpen}
            handleDelete={handleDeleteConfirm}
            handleClose={handleDeleteCancel}
            title="select response"
            handleLoad={isWorkflowDeleteLoading}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default CustomResponseComp;
