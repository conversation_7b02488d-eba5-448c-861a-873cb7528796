import { bgColors } from "../../utils/bgColors";
import { makeStyles } from "@mui/styles";
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Grid,
  IconButton,
  Popover,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import React, { useEffect, useState } from "react";
import EditIconSvg from "../../assets/svgs/EditIconSvg";
import DeleteIconSvg from "../../assets/svgs/DeleteIconSvg";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { fetchAllTemplatesByCompanyId } from "../../redux/slices/Templates/AllTemplatesSlice";
import { toastActions } from "../../utils/toastSlice";
import { removeTemplate } from "../../redux/slices/Templates/DeleteTemplateSlice";
import DeletePopUp from "../../components/common/DeletePopup";
import RestorePopUp from "../../components/common/RestorePopup";
import useDebouncedFetch from "../../utils/debounceHook";
import { checkAllTemplatesPermission } from "../../utils/permissions";
import NoAccessPage from "../../components/common/NoAccess";
import RestoreIconSvg from "../../assets/svgs/RestoreIconSvg";
import { restoreTemplate } from "../../redux/slices/Templates/RestoreTemplateSlice";
import { useLocation, useNavigate } from "react-router-dom";
import CommonTable, { TableColumn } from "../../components/common/CommonTable";
import CustomMainFilterPopover from "../../components/common/CustomMainFilerPopover";
import {
  mainFilterOptions,
  categoryOptions,
  statusOptions,
  mediaTypes,
} from "../../utils/constants";
import RedDeleteIconSvg from "../../assets/svgs/RedDeleteIconSvg";
import CloseIconSvg from "../../assets/svgs/CloseIconSvg";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
    height: "100%",
    width: "100%",
    display: "flex",
    flexDirection: "column",
  },
  searchField: {
    width: "90%",
    borderRadius: "12px",
    // height: "38px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  manageContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  grayColor: {
    color: `${bgColors.black1} !important`,
    // opacity: "60% !important",
  },

  messageInnerContainer: {
    border: "2px solid #F2F2F2",
    borderRadius: "6px",
    paddingInline: "4px",
    display: "flex",
    flexDirection: "row",
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
  },
  rotatedIcon: {
    cursor: "pointer",
    paddingRight: "5px",
    transform: "rotate(180deg)",
  },
  popoverContent: {
    padding: "10px",
    display: "flex",
    flexDirection: "column",
    gap: "10px",
    textAlign: "left",
  },
  table: {
    borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
      height: "35.8px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    // backgroundColor: bgColors.green,
    // color: bgColors.white,
    borderRadius: "8px",
    width: "120px",
    height: "32px",
    cursor: "pointer",
  },
  statusApproved: {
    backgroundColor: bgColors.green,
    color: bgColors.white,
    borderRadius: "20px",
    width: "100px",
    height: "25px",
    textAlign: "center",
    padding: "3px",
    fontSize: "12px",
  },
  statusPending: {
    backgroundColor: bgColors.yellow,
    color: bgColors.white,
    borderRadius: "20px",
    width: "100px",
    height: "25px",
    textAlign: "center",
    padding: "3px",
    fontSize: "12px",
  },
  statusRejected: {
    backgroundColor: bgColors.red,
    color: bgColors.white,
    borderRadius: "20px",
    width: "100px",
    height: "25px",
    textAlign: "center",
    padding: "3px",
    fontSize: "12px",
  },
  statusDrafted: {
    backgroundColor: bgColors.blue,
    color: bgColors.white,
    borderRadius: "20px",
    width: "100px",
    height: "25px",
    textAlign: "center",
    padding: "3px",
    fontSize: "12px",
  },
  statusDeleted: {
    backgroundColor: bgColors.black,
    color: bgColors.white,
    borderRadius: "20px",
    width: "100px",
    height: "25px",
    textAlign: "center",
    padding: "3px",
    fontSize: "12px",
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
});

const AllTemplates = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [tenantId, setTenantId] = useState(queryParams.get("tenantId"));

  const templatesSlice = useAppSelector(
    (state: any) => state?.allTemplatesData
  );
  const removeTemplateSlice = useAppSelector(
    (state: any) => state?.removeTemplateData
  );
  const restoreTemplateSlice = useAppSelector(
    (state: any) => state?.restoreTemplateData
  );
  const updateTemplateSlice = useAppSelector(
    (state: any) => state?.updateTemplateData
  );
  const createTemplateSlice = useAppSelector(
    (state: any) => state?.createTemplateData
  );
  const allTemplatesData = templatesSlice?.data?.data;

  const debouncedFetchTemplates = useDebouncedFetch(
    fetchAllTemplatesByCompanyId,
    500
  );
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );

  const templatesPermissionsArray = getuserPermissionData?.Templates;

  const hasAllTemplatesPermission = checkAllTemplatesPermission(
    templatesPermissionsArray
  );

  const allTemplatesPermissionsObject = templatesPermissionsArray?.find(
    (item: any) => Object.prototype.hasOwnProperty.call(item, "allTemplates")
  );

  const allTemplatesPermissionsActions = allTemplatesPermissionsObject
    ? allTemplatesPermissionsObject.allTemplates
    : [];

  const [isDeleteTemplateLoading, setIsDeleteTemplateLoading] = useState(false);
  const [isRestoreTemplateLoading, setIsRestoreTemplateLoading] =
    useState(false);
  const [
    newTemplatePermissionTooltipOpen,
    setNewTemplatePermissionTooltipOpen,
  ] = useState(false);
  const [
    editTemplatePermissionTooltipOpen,
    setEditTemplatePermissionTooltipOpen,
  ] = useState("");
  const [
    deleteTemplatePermissionTooltipOpen,
    setDeleteTemplatePermissionTooltipOpen,
  ] = useState("");
  const [
    restoreTemplatePermissionTooltipOpen,
    setRestoreTemplatePermissionTooltipOpen,
  ] = useState("");
  const [selectedFilter, setSelectedFilter] = React.useState({
    id: "1",
    value: "View All",
  });

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [pageNumber, setPageNumber] = React.useState(1);
  const [search, setSearch] = React.useState("");
  const [openDeletePopup, setOpenDeletePopup] = useState(false);
  const [openRestorePopup, setOpenRestorePopup] = useState(false);
  const [deleteToBeId, setDeleteToBeId] = useState("");
  const [restoreToBeId, setRestoreToBeId] = useState("");
  const [rowsPerPage] = useState(20);
  const [pageData, setPageData] = useState([]);
  const [detailsMap, setDetailsMap] = useState<{ [key: string]: boolean }>({});
  const [anchorElMap, setAnchorElMap] = useState<{
    [key: string]: HTMLElement | null;
  }>({});

  const hasAccess = (permission: any) => {
    if (allTemplatesPermissionsActions?.includes(permission)) {
      return true;
    }
    return false;
  };
  const navigate = useNavigate();
  const handleDeletePopup = (id: any) => {
    const hasPermissionToDeleteTemplate = hasAccess("deleteTemplate");
    if (hasPermissionToDeleteTemplate) {
      setOpenDeletePopup(true);
      setDeleteToBeId(id);
    } else {
      setDeleteTemplatePermissionTooltipOpen(id);
      setTimeout(() => {
        setDeleteTemplatePermissionTooltipOpen("");
      }, 2000);
    }
  };
  const handleTemplateAction = (action: string, templateId: string) => {
    const hasPermissionToAddTemplate = hasAccess("newTemplate");
    const hasPermissionToEditTemplate = hasAccess("editTemplate");
    if (action === "add" && hasPermissionToAddTemplate) {
      if (tenantId) {
        navigate(`/templates?tenantId=${tenantId}`, {
          state: { from: "/templates-all" },
        });
      } else {
        navigate("/templates", { state: { from: "/templates-all" } });
      }
    } else if (action === "add" && !hasPermissionToEditTemplate) {
      setNewTemplatePermissionTooltipOpen(true);
      setTimeout(() => {
        setNewTemplatePermissionTooltipOpen(false);
      }, 2000);
    } else if (action === "edit" && hasPermissionToEditTemplate) {
      if (tenantId) {
        navigate(`/templates/${templateId}?tenantId=${tenantId}`, {
          state: { from: "/templates-all" },
        });
      } else {
        navigate(`/templates/${templateId}`, {
          state: { from: "/templates-all" },
        });
      }
    } else {
      setEditTemplatePermissionTooltipOpen(templateId);
      setTimeout(() => {
        setEditTemplatePermissionTooltipOpen("");
      }, 2000);
    }
  };
  const handleRestorePopup = (id: any) => {
    const hasPermissionToRestoreTemplate = hasAccess("restoreTemplate");
    if (hasPermissionToRestoreTemplate) {
      setOpenRestorePopup(true);
      setRestoreToBeId(id);
    } else {
      setRestoreTemplatePermissionTooltipOpen(id);
      setTimeout(() => {
        setRestoreTemplatePermissionTooltipOpen("");
      }, 2000);
    }
  };

  const handleDeletePopupClose = () => {
    setOpenDeletePopup(false);
    setDeleteToBeId("");
  };

  const handleRestorePopupClose = () => {
    setOpenRestorePopup(false);
    setRestoreToBeId("");
  };

  const handleTemplateFilter = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCloseFilterPopover = () => {
    setAnchorEl(null);
  };

  const handleOptionClick = (option: any) => {
    if (option?.id === "1") {
      setSelectedFilter(option);
    }
    setPageNumber(1);
    handleCloseFilterPopover();
  };

  const handleDeleteTemplate = async (templateId: string) => {
    setIsDeleteTemplateLoading(true);
    const deleteData = {
      businessId: userData?.companyId,
      userId: userData?.userId,
      templateId: templateId,
    };
    try {
      const deleteResponse: any = await dispatch(removeTemplate(deleteData));
      if (deleteResponse?.meta?.requestStatus === "fulfilled") {
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: deleteResponse?.payload?.message,
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: deleteResponse?.payload,
          })
        );
      }
      handleDeletePopupClose();
    } catch (err: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: err?.message,
        })
      );
      handleDeletePopupClose();
    }
    setIsDeleteTemplateLoading(false);
  };

  const handleRestoreTemplate = async (templateId: any) => {
    setIsRestoreTemplateLoading(true);
    const restoreData = {
      businessId: userData?.companyId,
      userId: userData?.userId,
      templateId: templateId,
    };

    try {
      const restoreResponse: any = await dispatch(restoreTemplate(restoreData));
      if (restoreResponse?.meta?.requestStatus === "fulfilled") {
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: restoreResponse?.payload?.message,
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: restoreResponse?.payload,
          })
        );
      }
      handleRestorePopupClose();
    } catch (err: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: err?.message,
        })
      );
    }
    setIsRestoreTemplateLoading(false);
  };

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    newPage: number
  ) => {
    setPageNumber(newPage);
  };

  useEffect(() => {
    const postData = {
      userId: userData?.userId,
      businessId: userData?.companyId,
      pageNumber: pageNumber,
      per_page: rowsPerPage,
      filters: {
        searching: {
          value: search,
        },
        sorting: {
          column: "",
          order: "",
        },
        filtering: {
          filterType: "and",
          conditions: [
            {
              column:
                selectedFilter?.id.split("-").length > 0
                  ? selectedFilter?.id.split("-")[0]
                  : "",
              operator: "equal",
              value:
                selectedFilter?.id.split("-").length > 0
                  ? selectedFilter?.id.split("-")[1]
                  : "",
            },
          ],
        },
      },
    };
    debouncedFetchTemplates(postData);
  }, [
    dispatch,
    search,
    selectedFilter,
    pageNumber,
    createTemplateSlice,
    updateTemplateSlice,
    removeTemplateSlice,
    restoreTemplateSlice,
  ]);

  const getStatusButton = (status: any) => {
    switch (status) {
      case 1:
        return { label: "Pending", color: "#ff9800" }; // Warning (Orange)
      case 2:
        return { label: "Approved", color: "#4caf50" }; // Success (Green)
      case 3:
        return { label: "Rejected", color: "#c6131b" }; // Secondary (Purple)
      case 4:
        return { label: "Draft", color: "#1976d2" }; // Primary (Blue)
      case 5:
        return { label: "Deleted", color: "#000" }; // Success (Green)
      default:
        return { label: "", color: "" }; // Default (Gray)
    }
  };

  useEffect(() => {
    if (allTemplatesData) {
      setPageData(allTemplatesData);
    }
  }, [allTemplatesData]);

  const formatDate = (datetime: any) => {
    const date = new Date(datetime);
    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    return `${day} ${month} ${year}`;
  };
  const handleInfoClick = (
    event: React.MouseEvent<HTMLElement>,
    templateId: string
  ) => {
    setAnchorElMap((prev) => ({
      ...prev,
      [templateId]: event.currentTarget,
    }));
    setDetailsMap((prev) => ({
      ...prev,
      [templateId]: true,
    }));
  };

  const handleInfoClose = (templateId: string) => {
    setAnchorElMap((prev) => ({
      ...prev,
      [templateId]: null,
    }));
    setDetailsMap((prev) => ({
      ...prev,
      [templateId]: false,
    }));
  };
  const columns: TableColumn[] = [
    {
      id: "templateName",
      label: "Template Name",
      width: "120px",
      format: (value: string) => (
        <Box sx={{ display: "flex", alignItems: "center", pl: 2 }}>{value}</Box>
      ),
      align: "left",
    },
    {
      id: "createdBy",
      label: "Created by",
      width: "100px",
      align: "left",
    },
    {
      id: "category",
      label: "Category",
      width: "100px",
      align: "left",
      format: (value: string) => (
        <Box sx={{ display: "flex", alignItems: "start" }}>
          {categoryOptions[Number(value) - 1]?.value}
        </Box>
      ),
    },
    {
      id: "subCategory",
      label: "SubCategory",
      width: "100px",
      align: "left",
    },
    {
      id: "status",
      label: "Status",
      width: "100px",
      align: "left",
      format: (value: string) => {
        const { label, color } = getStatusButton(value);
        return (
          <Box
            sx={{
              backgroundColor: color,
              color: bgColors.white,
              borderRadius: "20px",
              width: "100px",
              height: "25px",
              textAlign: "center",
              padding: "3px",
              fontSize: "12px",
            }}
          >
            {label}
          </Box>
        );
      },
    },
    {
      id: "date",
      label: "Created Date",
      width: "100px",
      align: "left",
      format: (value: string) => (
        <Box sx={{ display: "flex", alignItems: "center", pl: 2 }}>
          {formatDate(value)}
        </Box>
      ),
    },
  ];

  const renderActions = (row: any) => {
    return (
      <Box sx={{ display: "flex", gap: 1 }}>
        {Number(row?.mediaType || 0) === 6 ? (
          Number(row?.status) === 4 && (
            <Tooltip
              open={editTemplatePermissionTooltipOpen === row.templateId}
              title="You don't have permission to edit template"
            >
              <IconButton
                onClick={() => handleTemplateAction("edit", row.templateId)}
                size="small"
              >
                <EditIconSvg />
              </IconButton>
            </Tooltip>
          )
        ) : (
          <Tooltip
            open={editTemplatePermissionTooltipOpen === row.templateId}
            title="You don't have permission to edit template"
          >
            <IconButton
              onClick={() => handleTemplateAction("edit", row.templateId)}
              size="small"
            >
              <EditIconSvg />
            </IconButton>
          </Tooltip>
        )}

        {row.status === "Deleted" ? (
          <Tooltip
            open={restoreTemplatePermissionTooltipOpen === row.templateId}
            title="You don't have permission to restore template"
          >
            <IconButton
              onClick={() => handleRestorePopup(row.templateId)}
              size="small"
            >
              <RestoreIconSvg />
            </IconButton>
          </Tooltip>
        ) : (
          <Tooltip
            open={deleteTemplatePermissionTooltipOpen === row.templateId}
            title="You don't have permission to delete template"
          >
            <IconButton
              onClick={() => handleDeletePopup(row.templateId)}
              size="small"
            >
              <DeleteIconSvg />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );
  };
  const renderTemplatesInMobile = (row: any) => {
    return (
      <>
        <Card key={row.templateId} sx={{ padding: 2, mb: 2 }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              marginBottom: 8,
            }}
          >
            <Box>
              <Typography variant="subtitle1" fontWeight="500">
                {row.templateName}
              </Typography>
            </Box>
            <Box>
              <Chip
                label={getStatusButton(row.status).label}
                sx={{
                  backgroundColor: getStatusButton(row.status).color,
                  color: "#fff",
                  "&:hover": {
                    backgroundColor: getStatusButton(row.status).color,
                  },
                }}
                size="small"
              />
              <IconButton
                size="small"
                onClick={(e) => handleInfoClick(e, row.templateId)}
                sx={{
                  color: "#666",
                  "&:hover": { color: "#3b82f6" },
                }}
              >
                <InfoOutlinedIcon fontSize="small" />
              </IconButton>
            </Box>
          </div>

          <CardContent
            sx={{ padding: 0, "&:last-child": { paddingBottom: 0 } }}
          >
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Created by
                </Typography>
                <Typography>{row.createdBy}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Category
                </Typography>
                <Typography>
                  {categoryOptions[Number(row.category) - 1]?.value}
                </Typography>
              </Grid>
            </Grid>

            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 8,
              }}
            >
              <IconButton
                onClick={() => handleTemplateAction("edit", row.templateId)}
                size="small"
                disabled={!hasAccess("editTemplate")}
              >
                <EditIconSvg />
              </IconButton>
              <IconButton
                onClick={() => handleTemplateAction("delete", row.templateId)}
                size="small"
                disabled={!hasAccess("deleteTemplate")}
              >
                <DeleteIconSvg />
              </IconButton>
            </div>
          </CardContent>
        </Card>

        <Popover
          open={Boolean(detailsMap[row.templateId])}
          anchorEl={anchorElMap[row.templateId]}
          onClose={() => handleInfoClose(row.templateId)}
          anchorOrigin={{
            vertical: "center",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "center",
            horizontal: "left",
          }}
          PaperProps={{
            sx: {
              maxWidth: "300px",
              p: 2,
              boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
            },
          }}
        >
          <Box sx={{ width: "auto", position: "relative" }}>
            <IconButton
              onClick={() => handleInfoClose(row.templateId)}
              sx={{
                position: "absolute",
                top: 0,
                right: 0,
                color: "red",
              }}
              size="small"
              aria-label="close"
            >
              <CloseIconSvg />
            </IconButton>
            <Grid container spacing={1}>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Template Name
                </Typography>
                <Typography>{row.templateName}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Status
                </Typography>
                <Typography>
                  <Chip
                    label={getStatusButton(row.status).label}
                    size="small"
                    sx={{
                      backgroundColor: getStatusButton(row.status).color,
                      color: "#fff",
                      fontWeight: "normal",
                      fontSize: "0.75rem",
                    }}
                  />
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Category
                </Typography>
                <Typography>
                  {categoryOptions[Number(row.category) - 1]?.value}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Sub Category
                </Typography>
                <Typography>{row.subCategory || "N/A"}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Created Date
                </Typography>
                <Typography>{formatDate(row.date)}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">
                  Created by
                </Typography>
                <Typography>{row.createdBy}</Typography>
              </Grid>
            </Grid>
          </Box>
        </Popover>
      </>
    );
  };

  const transformedData = pageData?.map((row: any) => ({
    templateId: row.templateId,
    templateName: row.templateName,
    createdBy: row.createdby,
    category: row.category,
    subCategory: row.subCategory,
    status: row.status,
    date: row.createdDate,
    mediaType: row.mediaType,
  }));

  return (
    <>
      {hasAllTemplatesPermission ? (
        <Grid className={classes.mainContainer}>
          <Box className={classes.bgContainer}>
            {/* Your existing header content */}
            <Box style={{ flex: "1", overflow: "hidden" }}>
              <CommonTable
                columns={columns}
                actions={renderActions}
                renderOnMobile={renderTemplatesInMobile}
                data={transformedData}
                rowIdKey="templateId"
                isLoading={templatesSlice?.status === "loading"}
                title="Templates"
                searchProps={{
                  value: search,
                  onChange: setSearch,
                  placeholder: "Search templates...",
                }}
                primaryAction={{
                  label: "Add Template",
                  onClick: () => handleTemplateAction("add", ""),
                  disabled: !hasAccess("newTemplate"),
                  tooltip: !hasAccess("newTemplate")
                    ? "You don't have permission to add template"
                    : "",
                }}
                selectedMainFilter={selectedFilter}
                handleMainFilter={handleTemplateFilter}
                showPagination={true}
                page={pageNumber}
                onPageChange={handlePageChange}
                totalPages={Math.ceil(
                  templatesSlice?.data?.total / rowsPerPage
                )}
                count={templatesSlice?.data?.total}
                perPage={rowsPerPage}
              />
            </Box>
          </Box>

          <CustomMainFilterPopover
            anchorEl={anchorEl}
            handleClose={handleCloseFilterPopover}
            mainFilterOptions={mainFilterOptions}
            subFilterOptions={categoryOptions}
            nestedFilterOptions={statusOptions}
            handleOptionClick={handleOptionClick}
            setSelectedFilter={setSelectedFilter}
          />

          <DeletePopUp
            title="Template"
            open={openDeletePopup}
            handleClose={handleDeletePopupClose}
            handleDelete={() => {
              handleDeleteTemplate(deleteToBeId);
            }}
            handleLoad={isDeleteTemplateLoading}
          />
          <RestorePopUp
            title="Template"
            open={openRestorePopup}
            handleClose={handleRestorePopupClose}
            handleRestore={() => {
              handleRestoreTemplate(restoreToBeId);
            }}
            handleLoad={isRestoreTemplateLoading}
          />
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default AllTemplates;
