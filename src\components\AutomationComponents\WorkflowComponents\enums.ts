export enum ConditionOperator {
  "Equals" = 0,
  "Not Equals" = 1,
  "Greater Than" = 2,
  "Less Than" = 3,
  "Contains" = 4,
  "Starts With" = 5,
  "Ends With" = 6,
}

export enum NodeType {
  FlowStart = "FlowStart",
  InteractiveMessage = "InteractiveMessage",
  Template = "Template",
  HttpRequest = "HttpRequest",
  Condition = "Condition",
  MediaType = "MediaType",
}

export enum InteractiveType {
  None = 0,
  Button = 1,
  List = 2,
}

export enum MediaType {
  NONE = 1,
  TEXT = 2,
  IMAGE = 3,
  VIDEO = 4,
  DOCUMENT = 5,
  CAROUSEL = 6,
}

// LeadSource.ts
export enum LeadSource {
  Any = -1,
  Direct = 0,
  IVR,
  Facebook,
  LinkedIn,
  GoogleAds,
  MagicBricks,
  NinetyNineAcres,
  Housing,
  GharOffice,
  Referral,
  WalkIn,
  Website,
  Gmail,
  PropertyMicrosite,
  PortfolioMicrosite,
  Phonebook,
  CallLogs,
  LeadPool,
  SquareYards,
  QuikrHomes,
  JustLead,
  WhatsApp,
  YouTube,
  QRCode,
  Instagram,
  OLX,
  EstateDekho,
  GoogleSheet,
  ChannelPartner,
  RealEstateIndia,
  CommonFloor,
  Data,
  RoofandFloor,
  MicrosoftAds,
  PropertyWala,
  ProjectMicrosite,
  MyGate,
  Flipkart,
  PropertyFinder,
  Bayut,
  Dubizzle,
  Webhook,
  TikTok,
  Snapchat,
}

export const LeadSourceLabels: Record<LeadSource, string> = {
  [LeadSource.Any]: "Any",
  [LeadSource.Direct]: "Direct",
  [LeadSource.IVR]: "IVR",
  [LeadSource.Facebook]: "Facebook",
  [LeadSource.LinkedIn]: "LinkedIn",
  [LeadSource.GoogleAds]: "Google Ads",
  [LeadSource.MagicBricks]: "MagicBricks",
  [LeadSource.NinetyNineAcres]: "99 Acres",
  [LeadSource.Housing]: "Housing",
  [LeadSource.GharOffice]: "Ghar Office",
  [LeadSource.Referral]: "Referral",
  [LeadSource.WalkIn]: "Walk-In",
  [LeadSource.Website]: "Website",
  [LeadSource.Gmail]: "Gmail",
  [LeadSource.PropertyMicrosite]: "Property Microsite",
  [LeadSource.PortfolioMicrosite]: "Portfolio Microsite",
  [LeadSource.Phonebook]: "Phonebook",
  [LeadSource.CallLogs]: "Call Logs",
  [LeadSource.LeadPool]: "Lead Pool",
  [LeadSource.SquareYards]: "Square Yards",
  [LeadSource.QuikrHomes]: "Quikr Homes",
  [LeadSource.JustLead]: "Just Lead",
  [LeadSource.WhatsApp]: "WhatsApp",
  [LeadSource.YouTube]: "YouTube",
  [LeadSource.QRCode]: "QR Code",
  [LeadSource.Instagram]: "Instagram",
  [LeadSource.OLX]: "OLX",
  [LeadSource.EstateDekho]: "Estate Dekho",
  [LeadSource.GoogleSheet]: "Google Sheet",
  [LeadSource.ChannelPartner]: "Channel Partner",
  [LeadSource.RealEstateIndia]: "RealEstateIndia",
  [LeadSource.CommonFloor]: "Common Floor",
  [LeadSource.Data]: "Data",
  [LeadSource.RoofandFloor]: "Roof and Floor",
  [LeadSource.MicrosoftAds]: "Microsoft Ads",
  [LeadSource.PropertyWala]: "PropertyWala",
  [LeadSource.ProjectMicrosite]: "Project Microsite",
  [LeadSource.MyGate]: "MyGate",
  [LeadSource.Flipkart]: "Flipkart",
  [LeadSource.PropertyFinder]: "Property Finder",
  [LeadSource.Bayut]: "Bayut",
  [LeadSource.Dubizzle]: "Dubizzle",
  [LeadSource.Webhook]: "Webhook",
  [LeadSource.TikTok]: "TikTok",
  [LeadSource.Snapchat]: "Snapchat",
};
