import { Box } from "@mui/system";
import { useEffect, useState } from "react";
import { NodeProps, Position, useReactFlow, Edge } from "reactflow";
import { bgColors } from "../../../utils/bgColors";
import { Button, IconButton, Paper, Typography } from "@mui/material";
import MediaTypePanel from "./MediaTypePanel";
import CustomHandle from "./CustomHandle";
import { parseTextToHtml } from "../../../utils/functions";
import { convertFromRaw } from "draft-js";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import { useWorkflow } from "../../../contexts/WorkflowContext";
import { MediaType } from "./enums";

interface MediaTypeNodeProps extends NodeProps {
  data: {
    selectedAction: any;
    isMessagePanelOpen: boolean;
    selectedMediaType: MediaType;
    message: string;
    mediaUrl?: string;
    mediaCaption?: string;
    footer?: string;
    buttons: any[];
    variables: any;
    showSaveUserResponse: boolean;
    saveResponseType: string;
    response: any;
    selectedUserResponse: any;
    editSelectResponseId: string;
    deleteSelectResponseId: string;
    isValid?: boolean;
    validationErrors?: string[];
  };
}

const MediaTypeNode = ({ data, id }: MediaTypeNodeProps) => {
  const {
    selectedAction,
    isMessagePanelOpen,
    selectedMediaType,
    message,
    mediaUrl,
    mediaCaption,
    footer,
    buttons,
    variables: initialVariables,
    showSaveUserResponse,
    saveResponseType,
    response,
    selectedUserResponse,
    editSelectResponseId,
    deleteSelectResponseId,
  } = data;

  const { handleNodeDelete } = useWorkflow();
  const [localVariables, setLocalVariables] = useState(initialVariables);

  useEffect(() => {
    if (initialVariables) {
      setLocalVariables(initialVariables);
    }
  }, [initialVariables]);

  const { setNodes, getEdges } = useReactFlow();
  const edges = getEdges();

  const updateNodeData = (updateFn: (data: any) => any) => {
    console.trace("updateNodeData called");
    setNodes((nds: any[]) =>
      nds.map((node: any) => {
        if (node.id === id) {
          const updatedData = updateFn(node.data);

          // Comprehensive validation logic for MediaType node
          if (updatedData.isValid === false) {
            let isValid = true;
            const validationErrors: string[] = [];

            // 1. Media Type validation
            if (updatedData.selectedMediaType !== 1) {
              // Not "None"
              if (
                updatedData.selectedMediaType === 2 &&
                !updatedData.mediaCaption
              ) {
                // Text type requires caption
                validationErrors.push("Text header value is required");
                isValid = false;
              } else if (
                updatedData.selectedMediaType === 3 &&
                !updatedData.mediaUrl
              ) {
                // Image type requires URL
                validationErrors.push("Image file is required");
                isValid = false;
              } else if (
                updatedData.selectedMediaType === 4 &&
                !updatedData.mediaUrl
              ) {
                // Video type requires URL
                validationErrors.push("Video file is required");
                isValid = false;
              } else if (
                updatedData.selectedMediaType === 5 &&
                !updatedData.mediaUrl
              ) {
                // Document type requires URL
                validationErrors.push("Document file is required");
                isValid = false;
              }
            }

            // 2. Message body validation
            if (!updatedData.message || updatedData.message.trim() === "") {
              validationErrors.push("Message content cannot be empty");
              isValid = false;
            }

            // 3. Variables validation
            if (updatedData.variables?.customMessageVariables?.length > 0) {
              const invalidVariables =
                updatedData.variables.customMessageVariables.filter(
                  (variable: any) => !variable.value || !variable.fallbackValue
                );
              if (invalidVariables.length > 0) {
                validationErrors.push(
                  "All variables must have value and fallback value"
                );
                isValid = false;
              }
            }

            // 4. Button validation
            if (!updatedData.buttons || updatedData.buttons.length === 0) {
              validationErrors.push("At least one button is required");
              isValid = false;
            } else if (updatedData.buttons.length > 3) {
              validationErrors.push("Maximum 3 buttons are allowed");
              isValid = false;
            } else {
              // Check if all buttons have text
              const emptyButtons = updatedData.buttons.filter(
                (button: any) => !button.text || button.text.trim() === ""
              );
              if (emptyButtons.length > 0) {
                validationErrors.push("All buttons must have text");
                isValid = false;
              }

              // Check button connections
              const connectedButtons = edges.filter(
                (edge) =>
                  edge.source === id &&
                  edge.sourceHandle &&
                  edge.sourceHandle.startsWith("right-buttonId-")
              );
              const isNodeConnected = edges.some(
                (edge) =>
                  edge.source === id &&
                  edge.sourceHandle === `right-nodeId-${id}`
              );

              if (
                connectedButtons.length < updatedData.buttons.length &&
                !isNodeConnected
              ) {
                validationErrors.push(
                  "Either connect all buttons to other nodes or connect this node directly"
                );
                isValid = false;
              }
            }

            updatedData.isValid = isValid;
            updatedData.validationErrors = validationErrors;
          }

          return { ...node, data: updatedData };
        }
        return node;
      })
    );
  };

  const handleEdit = () => {
    updateNodeData((data) => ({
      ...data,
      isMessagePanelOpen: !data.isMessagePanelOpen,
    }));
  };

  const handleDelete = () => {
    handleNodeDelete(id);
  };

  const handleMediaTypeChange = (mediaType: MediaType) => {
    updateNodeData((data) => ({
      ...data,
      mediaCaption: "",
      mediaUrl: "",
      selectedMediaType: mediaType,
    }));
  };

  const handleMessage = (messageTyped: string, newVariable: any) => {
    setLocalVariables({
      ...localVariables,
      customMessageVariables: newVariable,
    });
    updateNodeData((data) => ({
      ...data,
      variables: {
        ...(data.variables || {}),
        customMessageVariables: newVariable,
      },
      message: messageTyped,
    }));
  };

  const handleMediaUrl = (url: string) => {
    updateNodeData((data) => ({
      ...data,
      mediaUrl: url,
    }));
  };

  const handleMediaCaption = (caption: string) => {
    updateNodeData((data) => ({
      ...data,
      mediaCaption: caption,
    }));
  };

  const handleFooter = (footerText: string) => {
    updateNodeData((data) => ({
      ...data,
      footer: footerText,
    }));
  };

  const handleButtons = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      buttons: [...(data.buttons || []), value],
    }));
  };

  const handleButtonText = (buttonId: string, value: string) => {
    updateNodeData((data) => ({
      ...data,
      buttons: data.buttons.map((button: any) =>
        button.id === buttonId ? { ...button, text: value } : button
      ),
    }));
  };

  const handleRemoveButton = (buttonId: string) => {
    updateNodeData((data) => ({
      ...data,
      buttons: data.buttons.filter((button: any) => button.id !== buttonId),
    }));
  };

  const handleVariablesChange = (newVariable: any) => {
    setLocalVariables({
      ...localVariables,
      customMessageVariables: newVariable,
    });

    updateNodeData((data) => {
      return {
        ...data,
        variables: {
          ...(data.variables || {}),
          customMessageVariables: newVariable,
        },
      };
    });
  };

  const handleShowSaveUserResponse = () => {
    updateNodeData((data) => ({
      ...data,
      showSaveUserResponse: !data?.showSaveUserResponse,
    }));
  };

  const handleSaveResponseType = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      saveResponseType: value,
    }));
  };

  const handleSelectedResponse = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      selectResponse: value,
    }));
  };

  const handleResponse = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      response: value,
    }));
  };

  const handleEditSelectResponseId = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      editSelectResponseId: value,
    }));
  };

  const handleDeleteSelectResponseId = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      deleteSelectResponseId: value,
    }));
  };

  const formatContentForDisplay = (rawContent: string) => {
    try {
      const contentState = convertFromRaw(JSON.parse(rawContent));
      return contentState.getPlainText();
    } catch (error) {
      return rawContent;
    }
  };

  const isButtonConnected = (buttonId: string, allEdges: Edge[]) => {
    return allEdges.some(
      (edge) =>
        edge.source === id && edge.sourceHandle === `right-buttonId-${buttonId}`
    );
  };

  const getMediaTypeLabel = (type: MediaType) => {
    switch (type) {
      case MediaType.NONE:
        return "None";
      case MediaType.TEXT:
        return "Text";
      case MediaType.IMAGE:
        return "Image";
      case MediaType.VIDEO:
        return "Video";
      case MediaType.DOCUMENT:
        return "Document";
      default:
        return "None";
    }
  };

  const renderMediaPreview = () => {
    switch (selectedMediaType) {
      case MediaType.TEXT:
        return mediaCaption ? (
          <Typography sx={{ mt: 1, textAlign: "start" }}>
            {mediaCaption}
          </Typography>
        ) : (
          <Box
            sx={{
              mt: 1,
              p: 2,
              background: bgColors.gray2,
              borderRadius: 2,
              textAlign: "center",
            }}
          >
            <Typography variant="caption" color="textSecondary">
              Header text will appear here
            </Typography>
          </Box>
        );
      case MediaType.IMAGE:
        return mediaUrl ? (
          <Box sx={{ mt: 1, textAlign: "center" }}>
            <img
              src={mediaUrl}
              alt="Media preview"
              style={{
                maxWidth: "100%",
                maxHeight: "150px",
                borderRadius: "8px",
              }}
            />
          </Box>
        ) : (
          <Box
            sx={{
              mt: 1,
              p: 2,
              background: bgColors.gray2,
              borderRadius: 2,
              textAlign: "center",
            }}
          >
            <Typography variant="caption" color="textSecondary">
              Image will appear here
            </Typography>
          </Box>
        );
      case MediaType.VIDEO:
        return mediaUrl ? (
          <Box sx={{ mt: 1, textAlign: "center" }}>
            <video
              src={mediaUrl}
              controls
              style={{
                maxWidth: "100%",
                maxHeight: "150px",
                borderRadius: "8px",
              }}
            />
          </Box>
        ) : (
          <Box
            sx={{
              mt: 1,
              p: 2,
              background: bgColors.gray2,
              borderRadius: 2,
              textAlign: "center",
            }}
          >
            <Typography variant="caption" color="textSecondary">
              Video will appear here
            </Typography>
          </Box>
        );
      case MediaType.DOCUMENT:
        return mediaUrl ? (
          <Box
            sx={{
              mt: 1,
              p: 2,
              background: bgColors.green1,
              borderRadius: 2,
              textAlign: "center",
            }}
          >
            <Typography variant="body2" color={bgColors.green}>
              📄 Document attached
            </Typography>
          </Box>
        ) : (
          <Box
            sx={{
              mt: 1,
              p: 2,
              background: bgColors.gray2,
              borderRadius: 2,
              textAlign: "center",
            }}
          >
            <Typography variant="caption" color="textSecondary">
              Document will appear here
            </Typography>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: 2,
        position: "relative",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        width: 280,
      }}
    >
      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          backgroundColor: data.isValid === false ? "#FF7171" : bgColors.green,
          color: "white",
          padding: "8px 12px",
          borderTopLeftRadius: "6px",
          borderTopRightRadius: "6px",
        }}
      >
        <Typography variant="h6">Media Type</Typography>
        <Box display="flex" flexDirection="row">
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleEdit}
          >
            <EditOutlinedIcon />
          </IconButton>
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleDelete}
          >
            <DeleteOutlinedIcon />
          </IconButton>
        </Box>
        <Box sx={{ position: "absolute", top: 100, left: -2 }}>
          <CustomHandle
            type="target"
            position={Position.Left}
            id={`left-nodeId-${id}`}
          />
        </Box>
        <Box sx={{ position: "absolute", top: 100, right: -2 }}>
          <CustomHandle
            type="source"
            position={Position.Right}
            id={`right-nodeId-${id}`}
          />
        </Box>
      </Box>

      {/* Body */}
      <Box p={2}>
        {/* Validation Errors */}
        {data.isValid === false && data.validationErrors && (
          <Box
            sx={{
              backgroundColor: "#FFEEEE",
              p: 1,
              mb: 2,
              borderRadius: 1,
              border: "1px solid #FF7171",
            }}
          >
            <Box component="ul" sx={{ m: 0, pl: 2 }}>
              {data.validationErrors.map((error: string, index: number) => (
                <Typography
                  key={index}
                  component="li"
                  variant="caption"
                  color="error"
                >
                  {error}
                </Typography>
              ))}
            </Box>
          </Box>
        )}

        {/* Media Preview */}
        {renderMediaPreview()}

        {/* Message Content */}
        <Typography
          sx={{
            background: bgColors.green1,
            wordBreak: "break-word",
            whiteSpace: "pre-wrap",
            p: 2,
            borderRadius: 2,
            mt: 1,
          }}
        >
          <div
            dangerouslySetInnerHTML={{
              __html: parseTextToHtml(
                formatContentForDisplay(message) || "Message will display here"
              ),
            }}
          />
        </Typography>

        {/* Buttons */}
        {buttons?.map(({ text, id: buttonId }: any) =>
          text ? (
            <Button
              key={buttonId}
              variant="outlined"
              fullWidth
              sx={{
                background: bgColors.green1,
                borderRadius: 2,
                mt: 1,
                position: "relative",
                color: bgColors.green,
                "& .react-flow__handle": {
                  zIndex: 1002,
                },
              }}
            >
              {text}
              <Box
                sx={{
                  position: "absolute",
                  right: -2,
                  height: "100%",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <CustomHandle
                  type="source"
                  position={Position.Right}
                  id={`right-buttonId-${buttonId}`}
                />
              </Box>
            </Button>
          ) : (
            <Box
              key={buttonId}
              mt={1}
              sx={{ background: bgColors.green1, borderRadius: 2 }}
              p={1}
              textAlign={"center"}
            >
              Button Text will appear here
            </Box>
          )
        )}

        {/* Footer */}
        {footer ? (
          <Typography
            sx={{
              background: bgColors.gray2,
              p: 1,
              borderRadius: 1,
              mt: 1,
              fontSize: "0.75rem",
              textAlign: "center",
              color: "textSecondary",
            }}
          >
            {footer}
          </Typography>
        ) : (
          <Box
            sx={{
              background: bgColors.gray2,
              p: 1,
              borderRadius: 1,
              mt: 1,
              fontSize: "0.75rem",
              textAlign: "center",
              color: "textSecondary",
            }}
          >
            Footer will appear here
          </Box>
        )}
      </Box>

      {/* Editor Panel */}
      {isMessagePanelOpen && (
        <MediaTypePanel
          selectedMediaType={selectedMediaType}
          handleMediaTypeChange={handleMediaTypeChange}
          message={message}
          handleMessage={handleMessage}
          mediaUrl={mediaUrl}
          handleMediaUrl={handleMediaUrl}
          mediaCaption={mediaCaption}
          handleMediaCaption={handleMediaCaption}
          footer={footer}
          handleFooter={handleFooter}
          buttons={buttons}
          handleButtons={handleButtons}
          handleButtonText={handleButtonText}
          handleRemoveButton={handleRemoveButton}
          handleSidebar={handleEdit}
          variables={localVariables}
          handleVariablesChange={handleVariablesChange}
          showSaveUserResponse={showSaveUserResponse}
          handleShowSaveUserResponse={handleShowSaveUserResponse}
          saveResponseType={saveResponseType}
          handleSaveResponseType={handleSaveResponseType}
          response={response}
          selectedUserResponse={selectedUserResponse}
          editSelectResponseId={editSelectResponseId}
          handleSelectedResponse={handleSelectedResponse}
          handleResponse={handleResponse}
          handleEditSelectResponseId={handleEditSelectResponseId}
          deleteSelectResponseId={deleteSelectResponseId}
          handleDeleteSelectResponseId={handleDeleteSelectResponseId}
          updateNodeData={updateNodeData}
          workflowNodeId={id}
        />
      )}
    </Paper>
  );
};

export default MediaTypeNode;
