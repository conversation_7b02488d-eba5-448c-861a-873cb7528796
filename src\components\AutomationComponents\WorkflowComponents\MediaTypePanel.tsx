import {
  Box,
  IconButton,
  Paper,
  Typography,
  TextField,
  Button,
  Divider,
  FormControl,
  Select,
  MenuItem,
} from "@mui/material";
import { useState, useEffect } from "react";
import { bgColors } from "../../../utils/bgColors";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import { ContentState, EditorState } from "draft-js";
import { formatContent } from "../../../utils/functions";
import { MediaType } from "./enums";
import { v4 as uuidv4 } from "uuid";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import FileUpload from "../../common/FileUpload";
import { DraftEditorComponent } from "../../common/DraftEditorComponent";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { Autocomplete, InputAdornment } from "@mui/material";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import AddVariablePopup from "./AddVariablePopup";
import { reactDraftWysiwygToolbarOptionsarticle } from "../../../utils/react-draft-wysiwyg-options";
import {
  getNextVariableCount,
  insertTextAtCursor,
  extractVariables,
} from "./functions";
import VariableAssignmentComponentReactflow from "./variableAssignmentComponentReactflow";

const VariableAttributesData = [
  { name: "Name", value: "Name" },
  { name: "Contact", value: "Contact" },
  { name: "CountryCode", value: "CountryCode" },
  { name: "CountryName", value: "CountryName" },
  { name: "Email", value: "Email" },
  { name: "LeadStatus", value: "LeadStatus" },
  { name: "LeadSubStatus", value: "LeadSubStatus" },
  { name: "ScheduledAt", value: "ScheduledAt" },
  { name: "Project", value: "Project" },
];

const MediaTypePanel = ({
  selectedMediaType,
  handleMediaTypeChange,
  message,
  handleMessage,
  mediaUrl,
  handleMediaUrl,
  mediaCaption,
  handleMediaCaption,
  footer,
  handleFooter,
  buttons,
  handleButtons,
  handleButtonText,
  handleRemoveButton,
  handleSidebar,
  variables,
  handleVariablesChange,
  showSaveUserResponse,
  handleShowSaveUserResponse,
  saveResponseType,
  handleSaveResponseType,
  response,
  selectedUserResponse,
  editSelectResponseId,
  handleSelectedResponse,
  handleResponse,
  handleEditSelectResponseId,
  deleteSelectResponseId,
  handleDeleteSelectResponseId,
  updateNodeData,
  workflowNodeId,
}: any) => {
  const dispatch = useAppDispatch();
  const [editorState, setEditorState] = useState(() =>
    typeof message === "string"
      ? EditorState.createWithContent(ContentState.createFromText(message))
      : EditorState.createEmpty()
  );

  // Get data from Redux for SaveUserResponse
  const data = useAppSelector(
    (state: any) => state.getSaveResponseAttribute.getSaveResponseAttributeData
  );
  const attributesData = data?.data;

  // Local state for selected variable (persisted in this component)
  const [selectedVariable, setSelectedVariable] = useState(
    selectedUserResponse || null
  );
  const [isAddNewPopupOpen, setIsAddNewPopupOpen] = useState(false);

  useEffect(() => {
    setSelectedVariable(selectedUserResponse || null);
  }, [selectedUserResponse]);

  // Handler for Save User Response selection
  const handleSaveUserResponseChange = async (_: any, newValue: any) => {
    if (newValue?.id === "add-new") {
      setIsAddNewPopupOpen(true);
    } else {
      setSelectedVariable(newValue);

      setTimeout(() => {
        updateNodeData((prev: any) => ({
          ...prev,
          selectedUserResponse: newValue,
        }));
      }, 0);
    }
  };

  const handlePopupClose = () => {
    setIsAddNewPopupOpen(false);
  };

  const handlePopupSuccess = () => {};

  // Ensure at least one button exists when component loads
  useEffect(() => {
    if (!buttons || buttons.length === 0) {
      const defaultButton = {
        id: uuidv4(),
        text: "",
      };
      handleButtons(defaultButton);
    }
  }, []);

  const handleEditorStateChange = (newEditorOrTextState: any, type: string) => {
    if (type === "editor") {
      const formattedContent = formatContent(
        newEditorOrTextState?.getCurrentContent()
      );
      const extractedVariables = extractVariables(
        newEditorOrTextState,
        variables?.customMessageVariables || []
      );
      handleMessage(formattedContent, extractedVariables);

      // Extract variables from the editor content

      // handleVariablesChange(extractedVariables);
    }
  };

  const handleAddVariable = () => {
    const nextCount = getNextVariableCount(editorState, "editor");
    const variableText = `{{${nextCount}}}`;
    const newEditorState = insertTextAtCursor(editorState, variableText);
    setEditorState(newEditorState);

    const currentVariables = variables?.customMessageVariables || [];
    const newVariable = {
      index: currentVariables.length,
      veriable: variableText,
      value: "",
      type: 1,
      fallbackValue: "",
      referenceTableType: 0,
    };
    const updatedVariables = [...currentVariables, newVariable];
    const formattedContent = formatContent(newEditorState.getCurrentContent());
    handleMessage(formattedContent, updatedVariables);
  };

  const handleRemoveVariable = (index: number) => {
    const currentVariables = variables?.customMessageVariables || [];
    const updatedVariables = currentVariables.filter(
      (_: any, i: number) => i !== index
    );
    handleVariablesChange(updatedVariables);
  };

  const handleVariablesValueChange = (newVariableObj: any) => {
    const updateArray = (array: any[]) =>
      array.map((item: any, index: number) =>
        index === newVariableObj.index ? { ...item, ...newVariableObj } : item
      );

    if (newVariableObj?.type === 1) {
      const updatedCustomVars = updateArray(
        variables?.customMessageVariables || []
      );
      handleVariablesChange(updatedCustomVars);
    }
  };

  const handleVariablesFallbackValueChange = (newVariableObj: any) => {
    const updateArray = (array: any[]) =>
      array.map((item: any, index: number) =>
        index === newVariableObj.index ? { ...item, ...newVariableObj } : item
      );

    if (newVariableObj?.type === 1) {
      const updatedCustomVars = updateArray(
        variables?.customMessageVariables || []
      );
      handleVariablesChange(updatedCustomVars);
    }
  };

  const handleAddButton = () => {
    if (buttons.length >= 3) return;
    const newButton = {
      id: uuidv4(),
      text: "",
    };
    handleButtons(newButton);
  };

  const handleMediaCaptionChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    handleMediaCaption(value);
  };

  const handleHeaderMediaChange = (file: any, event: File | null) => {
    // const file = event.target.files && event.target.files[0];
    const allowedImageTypes = ["image/jpeg", "image/png", "image/gif"];
    const allowedVideoTypes = ["video/mp4", "video/avi", "video/mpeg"];
    const allowedDocumentTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];
    let isValidFile = false;
    if (event) {
      switch (selectedMediaType) {
        case 3:
          isValidFile = allowedImageTypes?.includes(event.type);
          break;
        case 4:
          isValidFile = allowedVideoTypes?.includes(event.type);
          break;
        case 5:
          isValidFile = allowedDocumentTypes?.includes(event.type);
          break;
        default:
          isValidFile = false;
      }

      if (isValidFile) {
        handleMediaUrl(file?.payload);
      } else {
        alert(
          `Only ${
            selectedMediaType === 3
              ? allowedImageTypes
              : selectedMediaType === 4
              ? allowedVideoTypes
              : selectedMediaType === 5
              ? allowedDocumentTypes
              : ""
          } files are allowed.`
        );
        file = null; // Clear the input field if the file is invalid
      }
    }
  };

  const renderMediaTypeOptions = () => (
    <Box sx={{ mb: 3, display: "flex", alignItems: "center", gap: 2 }}>
      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
        Media Type
      </Typography>
      <FormControl
        size="small"
        sx={{
          minWidth: 120,
          "& .MuiOutlinedInput-root": {
            "&:hover fieldset": {
              borderColor: bgColors.green,
            },
            "&.Mui-focused fieldset": {
              borderColor: bgColors.green,
            },
          },
        }}
      >
        <Select
          labelId="media-type-select-label"
          value={selectedMediaType}
          onChange={(e) =>
            handleMediaTypeChange(Number(e.target.value) as MediaType)
          }
          sx={{
            fontSize: "12px",
          }}
        >
          <MenuItem value={MediaType.NONE}>
            <Typography sx={{ fontSize: "12px" }}>None</Typography>
          </MenuItem>
          <MenuItem value={MediaType.TEXT}>
            <Typography sx={{ fontSize: "12px" }}>Text</Typography>
          </MenuItem>
          <MenuItem value={MediaType.IMAGE}>
            <Typography sx={{ fontSize: "12px" }}>Image</Typography>
          </MenuItem>
          <MenuItem value={MediaType.VIDEO}>
            <Typography sx={{ fontSize: "12px" }}>Video</Typography>
          </MenuItem>
          <MenuItem value={MediaType.DOCUMENT}>
            <Typography sx={{ fontSize: "12px" }}>Document</Typography>
          </MenuItem>
        </Select>
      </FormControl>
    </Box>
  );

  const renderMediaUpload = () => {
    return (
      <Box sx={{ mb: 3 }}>
        {selectedMediaType === 2 && (
          <TextField
            inputRef={null}
            type="text"
            size="small"
            name="header"
            value={mediaCaption}
            onChange={handleMediaCaptionChange}
            fullWidth
            placeholder="Enter header text"
            sx={{
              "& input": {
                // fontWeight: "600",
              },
              "& .MuiOutlinedInput-root": {
                borderColor: bgColors.green,
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: bgColors.green,
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: bgColors.green,
                },
              },
            }}
          />
        )}
        {(selectedMediaType === 3 ||
          selectedMediaType === 4 ||
          selectedMediaType === 5) && (
          <>
            <FileUpload
              fileType={selectedMediaType}
              selectedFiles={mediaUrl}
              handleMediaChange={handleHeaderMediaChange}
              isFileValid={true}
            />
          </>
        )}
      </Box>
    );
  };

  const renderMessageEditor = () => (
    <Box sx={{ mb: 3 }}>
      <DraftEditorComponent
        editorState={editorState}
        handleEditorStateChange={(newEditorState: EditorState) => {
          setEditorState(newEditorState);
          handleEditorStateChange(newEditorState, "editor");
        }}
        reactDraftWysiwygToolbarOptionsarticle={
          reactDraftWysiwygToolbarOptionsarticle
        }
        handleAddVariable={handleAddVariable}
        selectedVariable={null}
      />
      {variables?.customMessageVariables?.length > 0 &&
        variables?.customMessageVariables?.map(
          (item: any) => item.type === 1
        ) && (
          <VariableAssignmentComponentReactflow
            variables={variables}
            editorState={editorState}
            variableType={1}
            handleVariablesChange={handleVariablesChange}
            handleRemoveVariable={handleRemoveVariable}
            onVariablesValueChange={handleVariablesValueChange}
            onVariableFallbackValueChange={handleVariablesFallbackValueChange}
            attributesData={VariableAttributesData}
          />
        )}
    </Box>
  );

  const renderButtonsSection = () => {
    // Ensure at least 1 button exists
    const buttonsToRender =
      buttons && buttons.length > 0 ? buttons : [{ id: uuidv4(), text: "" }];

    return (
      <Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            Buttons
          </Typography>
          <Button
            size="small"
            startIcon={<AddIcon />}
            onClick={handleAddButton}
            disabled={buttons && buttons.length >= 3}
            sx={{
              color: "#fff",
              backgroundColor: bgColors.green,
              "&:disabled": {
                color: bgColors.gray1,
                backgroundColor: bgColors.gray2,
              },
            }}
            variant="outlined"
          >
            Add Button {buttons && buttons.length >= 3 && "(Max 3)"}
          </Button>
        </Box>

        {buttonsToRender?.map((button: any, index: number) => (
          <Box key={button.id} sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
              <TextField
                fullWidth
                size="small"
                placeholder={`Button ${index + 1} text`}
                value={button.text || ""}
                onChange={(e) => handleButtonText(button.id, e.target.value)}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderColor: bgColors.green,
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: bgColors.green,
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: bgColors.green,
                    },
                  },
                }}
              />
              {buttonsToRender.length > 1 && (
                <IconButton
                  size="small"
                  onClick={() => handleRemoveButton(button.id)}
                  sx={{
                    color: bgColors.red,
                    "&:hover": {
                      backgroundColor: bgColors.red2,
                    },
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Box>
          </Box>
        ))}
      </Box>
    );
  };

  const renderFooter = () => (
    <Box sx={{ mb: 3 }}>
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
        Footer
      </Typography>
      <TextField
        fullWidth
        size="small"
        placeholder="Enter footer text (optional)"
        value={footer || ""}
        onChange={(e) => handleFooter(e.target.value)}
        multiline
        rows={2}
        sx={{
          "& .MuiOutlinedInput-root": {
            borderColor: bgColors.green,
            "&:hover .MuiOutlinedInput-notchedOutline": {
              borderColor: bgColors.green,
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderColor: bgColors.green,
            },
          },
        }}
      />
    </Box>
  );

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -360,
        top: 0,
        width: 350,
        height: 500,
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s ease",
        zIndex: 10,
      }}
      className="nowheel nopan nodrag"
    >
      {/* Header */}
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
          height: "50px",
        }}
      >
        <Typography variant="h6" color="white">
          Media Type Editor
        </Typography>
        <IconButton
          aria-label="close"
          sx={{
            color: `${bgColors.red}`,
            width: 48,
            height: 48,
            "& svg": {
              width: 40,
              height: 40,
              fill: `${bgColors.red1}`,
            },
          }}
          onClick={() => handleSidebar(false)}
        >
          <CloseIconSvg />
        </IconButton>
      </Box>

      {/* Content */}
      <Box
        sx={{
          flexGrow: 1,
          overflowY: "auto",
          padding: 2,
          height: "calc(500px - 50px)",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {renderMediaTypeOptions()}
        {selectedMediaType !== 1 && renderMediaUpload()}
        <Divider sx={{ mb: 2 }} />
        {renderMessageEditor()}
        <Divider sx={{ mb: 2 }} />
        {renderButtonsSection()}
        <Divider sx={{ mb: 2 }} />
        {renderFooter()}
        <Divider sx={{ mb: 2 }} />
        {/* Save User Response Section - Exact copy from CustomResponseComp */}
        <Box sx={{ mt: 1, mb: 2 }}>
          <Box sx={{ backgroundColor: bgColors.blue3, padding: 1 }}>
            <Typography
              style={{
                color: bgColors.green,
                fontWeight: "600",
                fontSize: "14px",
                marginBottom: 8,
              }}
            >
              Save User Response
            </Typography>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                gap: 1,
              }}
            >
              <Autocomplete
                options={[
                  { id: "add-new", veriableName: "+ Add New" },
                  ...(attributesData?.length > 0 ? attributesData : []),
                ]}
                getOptionLabel={(option) => option.name}
                value={selectedVariable || null}
                onChange={handleSaveUserResponseChange}
                disablePortal
                disableClearable
                renderInput={(params) => {
                  return (
                    <TextField
                      {...params}
                      placeholder="Select Variable"
                      size="small"
                      sx={{
                        minHeight: 32,
                        fontSize: 14,
                        background: "#fff",
                        borderRadius: "8px",
                      }}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <InputAdornment position="end">
                            {selectedVariable && (
                              <CancelOutlinedIcon
                                fontSize="small"
                                color="error"
                                style={{ cursor: "pointer", marginRight: 4 }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedVariable(null);
                                  updateNodeData((prev: any) => ({
                                    ...prev,
                                    selectedUserResponse: null,
                                  }));
                                }}
                              />
                            )}
                            {params.InputProps.endAdornment}
                          </InputAdornment>
                        ),
                      }}
                    />
                  );
                }}
                renderOption={(props, option) => {
                  if (option.id === "add-new") {
                    return (
                      <li {...props} key={option.id}>
                        <span
                          style={{ color: bgColors.green, fontWeight: 700 }}
                        >
                          {option.veriableName}
                        </span>
                      </li>
                    );
                  }
                  return (
                    <li {...props} key={option.id}>
                      {option.name}
                    </li>
                  );
                }}
                ListboxProps={{
                  style: {
                    maxHeight: 200,
                    overflow: "auto",
                    zIndex: 1300,
                    width: "100%",
                  },
                }}
                sx={{ width: "100%" }}
              />
            </Box>
          </Box>
          <AddVariablePopup
            open={isAddNewPopupOpen}
            onClose={handlePopupClose}
            onSuccess={handlePopupSuccess}
          />
        </Box>
      </Box>
    </Paper>
  );
};

export default MediaTypePanel;
