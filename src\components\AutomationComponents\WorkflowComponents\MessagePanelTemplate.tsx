import { Box, IconButton, Paper, Typography } from "@mui/material";
import { bgColors } from "../../../utils/bgColors";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import { useReactFlow } from "reactflow";
import TemplatePopUp from "../../InboxComponents/inboxDetailsComponents/TemplatePopUp";

interface MessagePanelTemplateNodeProps {
  id: string;
  handleEdit: () => void;
  toggleTemplateSelector: () => void;
  templateSelectorOpen: boolean;
  templateObj: any;
  localTemplate: any;
  setLocalTemplate: (template: any) => void;
  handleSendTemplatePayload: (payload: any, templateState: any) => void;
}

const MessagePanelTemplate = ({
  id,
  handleEdit,
  toggleTemplateSelector,
  templateSelectorOpen,
  localTemplate,
  handleSendTemplatePayload,
}: MessagePanelTemplateNodeProps) => {
  const { setNodes } = useReactFlow();

  const handleTemplateClick = () => {
    toggleTemplateSelector();
  };

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -320,
        top: 0,
        width: 300,
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s ease",
        zIndex: 10,
      }}
      className="nowheel nopan nodrag"
    >
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 1,
            width: "100%",
          }}
        >
          <Typography variant="h6" color="white">
            Send Template
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: `${bgColors.red}`,
              width: 48,
              height: 48,
              "& svg": {
                width: 40,
                height: 40,
                fill: `${bgColors.red1}`,
              },
            }}
            onClick={() => handleEdit()}
          >
            <CloseIconSvg />
          </IconButton>
        </Box>
      </Box>
      {localTemplate ? (
        <Box
          sx={{
            padding: "12px",
            border: "1px solid #25D366",
            borderRadius: "4px",
            backgroundColor: "rgba(37, 211, 102, 0.05)",
            maxWidth: "100%",
            overflow: "hidden",
          }}
        >
          <Typography
            fontWeight={500}
            color="#128C7E"
            variant="h6"
            sx={{
              wordBreak: "break-word",
              overflowWrap: "break-word",
            }}
          >
            {localTemplate.templateName}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              mt: 1,
              color: "#555",
              wordBreak: "break-word",
              overflowWrap: "break-word",
              whiteSpace: "pre-wrap",
              maxWidth: "100%",
            }}
          >
            {localTemplate.body}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              mt: 2,
              color: "#128C7E",
              textDecoration: "underline",
              cursor: "pointer",
            }}
            onClick={handleTemplateClick}
          >
            Change template
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            border: "1px dashed #25D366",
            borderRadius: "4px",
            cursor: "pointer",
            textAlign: "center",
            transition: "all 0.2s",
            "&:hover": {
              backgroundColor: "rgba(37, 211, 102, 0.05)",
            },
            m: 2,
          }}
          height={200}
          onClick={handleTemplateClick}
        >
          <Typography variant="body2">
            Click here to select a WhatsApp template
          </Typography>
        </Box>
      )}

      {templateSelectorOpen && (
        <TemplatePopUp
          open={templateSelectorOpen}
          handleCloseTemplatePopup={toggleTemplateSelector}
          setSendTemplatePayload={handleSendTemplatePayload}
        />
      )}
    </Paper>
  );
};

export default MessagePanelTemplate;
