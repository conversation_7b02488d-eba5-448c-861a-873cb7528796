import { EditorState, Modifier } from "draft-js";
export interface CurlRequest {
  method: string;
  protocol: string;
  host: string;
  path: string;
  query: string | null;
  port: number | null;
  headers: { key: string; value: string }[];
  body: string;
  url: string;
}

const dictionary = {
  Name: "Name",
  CountryCode: "CountryCode",
  CountryName: "CountryName",
  Contact: "Contact",
  Email: "Email",
};

export const dictionaryOptions = Object.entries(dictionary).map(
  ([key, value]) => ({
    key,
    value,
  })
);

export const ButtonOptions = [
  { value: "", label: "None" },
  { value: "button", label: "Button" },
  { value: "list", label: "List" },
];

export const variableOptions = [
  // { value: "User Trait", label: "User Trait" },
  // { value: "Workflow Variable", label: "Workflow Variable" },
  { value: "variable", label: "Variable" },
];

export const SaveResponseRadioOptions = [
  // { value: "trait", label: "User Trait" },
  // { value: "variable", label: "Workflow Variable" },
  { value: "variable", label: "Variable" },
];

export interface Veriables {
  veriable: string;
  value: string;
  fallbackValue: string;
  type?: string;
  index: number;
}

// export const getNextVariableCount = (editorState: EditorState): number => {
//   const content = editorState?.getCurrentContent()?.getPlainText();
//   const variableMatches = content?.match(/\{\{(\d+)\}\}/g);
//   if (!variableMatches) return 1;
//   const variableNumbers = variableMatches?.map((match) =>
//     parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
//   );
//   return Math.max(...variableNumbers) + 1;
// };

export const getNextVariableCount = (
  inputValue: EditorState | string,
  inputType: "editor" | "text"
): number => {
  let content = "";

  // Determine content based on inputType
  if (
    inputType === "editor" &&
    (inputValue as EditorState)?.getCurrentContent
  ) {
    // If input is of type editor, get the plain text from EditorState
    content = (inputValue as EditorState).getCurrentContent().getPlainText();
  } else if (inputType === "text") {
    // If input is plain string
    content = inputValue as string;
  }

  // Find matches for {{x}} variables
  const variableMatches = content.match(/\{\{(\d+)\}\}/g);
  if (!variableMatches) return 1;

  // Extract variable numbers
  const variableNumbers = variableMatches.map((match) =>
    parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
  );

  // Return the next variable count
  return Math.max(...variableNumbers) + 1;
};

export const validateVariables = (text: String) => {
  const variableMatches = text?.match(/\{\{(\d+)\}\}/g);
  const variableNumbers = variableMatches?.map((match) =>
    parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
  );
  let checkValidation = true;
  if (variableNumbers?.length) {
    if (variableNumbers[0] !== 1) {
      checkValidation = false;
      return checkValidation;
    }
    for (let i = 0; i < variableNumbers?.length - 1; i++) {
      if (Number(variableNumbers[i + 1]) !== Number(variableNumbers[i]) + 1) {
        checkValidation = false;
        return checkValidation;
      }
    }
  }
  return checkValidation;
};

// Get the count based on all header values, not just the specific index
export const getNextVariableCountFromAllHeaders = (headers: any[]) => {
  // Regular expression to find variables in the format {{n}}
  const variableRegex = /\{\{(\d+)\}\}/g;

  // Collect all variable numbers from the headers
  let maxVariableNumber = 0;

  headers.forEach((header) => {
    const matches = header?.value?.match(variableRegex);
    if (matches) {
      matches?.forEach((match: any) => {
        const number = parseInt(match?.replace(/[{}]/g, ""), 10);
        if (number > maxVariableNumber) {
          maxVariableNumber = number;
        }
      });
    }
  });

  // Return the next available variable count
  return maxVariableNumber + 1;
};

export const insertTextAtCursor = (
  editorState: EditorState,
  text: string
): EditorState => {
  const contentState = editorState.getCurrentContent();
  const selectionState = editorState.getSelection();
  const newContentState = Modifier.insertText(
    contentState,
    selectionState,
    text
  );
  return EditorState.push(editorState, newContentState, "insert-characters");
};

export const extractVariablesInWorkflows = (
  editorState: EditorState,
  existingVariables: any = []
): any => {
  const content = editorState.getCurrentContent().getPlainText();
  const variableRegex = /\{\{(\d+)\}\}/g;
  const matches: any = content.match(variableRegex);

  if (!matches)
    return existingVariables.filter((v: any) => content.includes(v.veriable));

  return matches?.map((match: any) => {
    const variable = match.replace(/\{\{|\}\}/g, "");
    const existingVar = existingVariables.find(
      (v: any) => v.veriable === `{{${variable}}}`
    );
    return (
      existingVar || {
        veriable: `{{${variable}}}`,
        type: "User Trait",
        value: "",
        fallbackValue: "",
      }
    );
  });
};

export const extractVariables = (
  editorState: EditorState,
  existingVariables: any = []
): any => {
  const content = editorState.getCurrentContent().getPlainText();
  const variableRegex = /\{\{(\d+)\}\}/g;
  const matches: any = content.match(variableRegex);

  if (!matches)
    return existingVariables?.filter((v: any) => content.includes(v.veriable));

  return matches?.map((match: any, index: number) => {
    const variable = match.replace(/\{\{|\}\}/g, "");
    const existingVar = existingVariables.find(
      (v: any) => v.veriable === `{{${variable}}}`
    );
    return (
      existingVar || {
        veriable: `{{${variable}}}`,
        type: 1,
        value: "",
        fallbackValue: "",
        referenceTableType: 0,
        index: existingVariables.length + index, // Assign next available index
      }
    );
  });
};

export const extractVeriablesFromString = (
  content: string,
  existingVeriables: Veriables[] = []
): Veriables[] => {
  const variableRegex = /\{\{(\d+)\}\}/g; // Regex to match {{number}}
  const matches = content?.match(variableRegex);

  if (!matches) {
    // If no matches found, filter the existing veriables
    return existingVeriables?.filter((v) => content?.includes(v.veriable));
  }

  // Map over the matches to extract veriable information
  return matches.map((match, index) => {
    const veriable = match.replace(/\{\{|\}\}/g, ""); // Extract number
    const existingVeriable = existingVeriables.find(
      (v) => v.veriable === `{{${veriable}}}`
    );
    return (
      existingVeriable || {
        veriable: `{{${veriable}}}`,
        type: "",
        value: "",
        fallbackValue: "",
        index: index,
      }
    );
  });
};

export const validateLength = (inputString: string, length: number) => {
  if (inputString.length > length) {
    return `Length exceeds ${length} characters`;
  }
  return null; // No error if length is valid
};

export function parseCurlCommand(curlCommand: string): CurlRequest | null {
  let requestMethod = "GET";
  let protocol = "";
  let host = "";
  let path = "";
  let query: string | null = null;
  let port: number | null = null;
  const headers: { key: string; value: string }[] = [];
  let body = "";

  // Method
  const methodMatch = curlCommand.match(/(?:--request|-X)\s+([A-Z]+)/);
  if (methodMatch) requestMethod = methodMatch[1];

  // URL
  const urlMatch = curlCommand.match(/['"]?(https?:\/\/[^\s'"]+)['"]?/);
  if (!urlMatch) return null;

  let urlStr = urlMatch[1];

  try {
    const url = new URL(urlStr);
    protocol = url.protocol.replace(":", "");
    host = url.hostname;
    path = url.pathname || "/";
    query = url.search ? url.search.slice(1) : null;
    port = url.port ? parseInt(url.port) : null;
  } catch (err) {
    console.error("Invalid URL:", err);
    return null;
  }

  // Headers
  const headerRegex = /(?:--header|-H)\s+['"]?([^'"]+)['"]?/g;
  let headerMatch;
  while ((headerMatch = headerRegex.exec(curlCommand)) !== null) {
    const [name, value] = headerMatch[1].split(":").map((part) => part.trim());
    if (name && value) {
      if (
        name.toLowerCase() === "content-type" ||
        name.toLowerCase() === "accept"
      ) {
        headers.unshift({ key: name, value }); // Ensure Content-Type is at index 0
      } else {
        headers.push({ key: name, value });
      }
    }
  }

  // Body
  const bodyMatch = curlCommand.match(
    /(?:--data(?:-raw)?|-d)\s+(["'])([\s\S]*?)\1/
  );

  if (bodyMatch) {
    body = bodyMatch[2];
    if (requestMethod === "GET") requestMethod = "POST";
  }

  return {
    method: requestMethod,
    protocol,
    host,
    path,
    query,
    port,
    headers,
    body,
    url: urlStr,
  };
}
