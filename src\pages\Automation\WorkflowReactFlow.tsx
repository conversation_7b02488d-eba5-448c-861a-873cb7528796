import { Box, FormControlLabel, Switch, Typography } from "@mui/material";
import React, { useState, useCallback, useRef, useEffect } from "react";
import ReactFlow, {
  Background,
  Controls,
  useNodesState,
  useEdgesState,
  useReactFlow,
  Node,
  Connection,
  MarkerType,
  addEdge,
  reconnectEdge,
  ConnectionLineType,
  Edge,
  Panel,
} from "reactflow";
import "reactflow/dist/style.css"; // Import styles for ReactFlow
import { bgColors } from "../../utils/bgColors";
import { v4 as uuidv4 } from "uuid";
import { useNavigate, useParams } from "react-router-dom";
import SendMessageNode from "../../components/AutomationComponents/WorkflowComponents/SendMessageNode";
import customEdge from "../../components/AutomationComponents/WorkflowComponents/CustomEdge";
import ConnectionLine from "../../components/AutomationComponents/WorkflowComponents/ConnectionLine";
import triggerWebhookNode from "../../components/AutomationComponents/WorkflowComponents/triggerWebhookNode";
import MediaTypeNode from "../../components/AutomationComponents/WorkflowComponents/MediaTypeNode";
import {
  MessageOutlined,
  TextsmsOutlined,
  ListAltOutlined,
  SendOutlined,
  IntegrationInstructionsOutlined,
} from "@mui/icons-material";
import FlowSidebar from "../../components/AutomationComponents/WorkflowComponents/FlowSidebar";
import { getSaveResponseAttribute } from "../../redux/slices/Workflows/getSaveUserResponseAttribute";
import {
  ConditionOperator,
  InteractiveType,
  NodeType,
} from "../../components/AutomationComponents/WorkflowComponents/enums";
import FlowStartNode from "../../components/AutomationComponents/WorkflowComponents/FlowStartNode";
import { updateWorkflowReactflow } from "../../redux/slices/Workflows/updateWorkflowReactflowSlice";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import ConditionNode from "../../components/AutomationComponents/WorkflowComponents/ConditionNode";
import TemplateNode from "../../components/AutomationComponents/WorkflowComponents/TemplateNode";
import { getAllWorkflowsReactflow } from "../../redux/slices/Workflows/getAllWorkflowsReactflowSlice";
import { toastActions } from "../../utils/toastSlice";
import CancelDialogPopup from "../../components/common/CancelDialogPop";
import { getWorkflowReactflowById } from "../../redux/slices/Workflows/getWorkflowReactflowByIdSlice";
import { WorkflowProvider } from "../../contexts/WorkflowContext";
import { getWorkflowAllKeywords } from "../../redux/slices/Workflows/getWorkflowAllKeywordsSlice";
import { createKeywordReactflow } from "../../redux/slices/Workflows/createKeywordsReactflowSlice";
import { GetAllStatusByTenantId } from "../../redux/slices/Utility/GetAllStatusByTenantId";
import { GetAllProjectsByTenantId } from "../../redux/slices/Utility/GetAllProjectsByTenantId";
import { getFlowstartNodes } from "../../redux/slices/Workflows/getFlowstartNodesSlice";

export interface ButtonListType {
  id: string;
  companyId: string;
  userId: string;
  listName: string;
  buttonName: string;
  inputs: { title: string; description: string }[];
}

export const messages = [
  {
    id: 1,
    icon: <MessageOutlined color="action" />,
    label: "Send a Message",
    description: "Send a WhatsApp message",
    value: InteractiveType.None,
    nodeType: NodeType.InteractiveMessage,
  },
  {
    id: 2,
    icon: <TextsmsOutlined color="action" />,
    label: "Text Button",
    description: "Add a text button",
    value: InteractiveType.Button,
    nodeType: NodeType.InteractiveMessage,
  },
  {
    id: 3,
    icon: <ListAltOutlined color="action" />,
    label: "List Button",
    description: "Create a list menu",
    value: InteractiveType.List,
    nodeType: NodeType.InteractiveMessage,
  },
  {
    id: 4,
    icon: <SendOutlined color="action" />,
    label: "Send Template",
    description: "Use message template",
    value: null,
    nodeType: NodeType.Template,
  },
  {
    id: 5,
    icon: <MessageOutlined color="action" />,
    label: "Media Type",
    description: "Send media with message",
    value: null,
    nodeType: NodeType.MediaType,
  },
];

export const actions = [
  {
    id: 1,
    icon: <TextsmsOutlined color="action" />,
    label: "Condition",
    description: "Add conditional logic",
    value: null,
    nodeType: NodeType.Condition,
  },
  {
    id: 2,
    icon: <IntegrationInstructionsOutlined color="action" />,
    label: "Trigger Webhook",
    description: "Call external API",
    value: null,
    nodeType: NodeType.HttpRequest,
  },
];

const nodeTypes = {
  [NodeType.InteractiveMessage]: SendMessageNode,
  [NodeType.HttpRequest]: triggerWebhookNode,
  [NodeType.FlowStart]: FlowStartNode,
  [NodeType.Condition]: ConditionNode,
  [NodeType.Template]: TemplateNode,
  [NodeType.MediaType]: MediaTypeNode,
};

const edgeTypes = {
  customEdge: customEdge,
};

const WorkflowReactFlow = () => {
  const initialNodes = (): Node[] => [
    {
      id: uuidv4(),
      type: NodeType.FlowStart,
      position: { x: 100, y: 100 },
      data: {
        keywords: [],
        leadSource: [],
        statusChange: [],
        leadProject: [],
        triggerType: "keywords" as
          | "keywords"
          | "newLead"
          | "statusChange"
          | "leadProject",
        isMessagePanelOpen: true,
        isSaved: false,

        isValid: true,
      },
    },
  ];

  const initialEdges: any[] = [];
  const { id } = useParams();
  const [workflowData, setWorkflowData] = useState<any>(null);
  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [tempWorkflowName, setTempWorkflowName] = useState("");
  const [isCancelPopupOpen, setIsCancelPopupOpen] = useState(false);
  const [actionsVisible, setActionsVisible] = useState(false);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes());

  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const { screenToFlowPosition, fitView, getViewport, setViewport } =
    useReactFlow();
  const isInitialLoad = useRef(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);

  const [isWorkflowActive, setIsWorkflowActive] = useState(true);
  const [isSavingWorkflow, setIsSavingWorkflow] = useState(false);
  const data = useAppSelector(
    (state: any) => state.getSaveResponseAttribute.getSaveResponseAttributeData
  );
  const attributesData = data?.data;

  // Add centralized node deletion handler
  const handleNodeDelete = useCallback(
    (nodeId: string) => {
      setNodes((prevNodes) => {
        const updatedNodes = prevNodes.filter((nd) => nd.id !== nodeId);

        // Remove edges connected to the deleted node
        setEdges((prevEdges) =>
          prevEdges.filter(
            (edge) => edge.source !== nodeId && edge.target !== nodeId
          )
        );

        // Fit view to the remaining nodes after a short delay
        setTimeout(() => {
          fitView({
            nodes: updatedNodes,
            duration: 800,
            padding: 0.2,
            minZoom: 0.3,
            maxZoom: 0.7,
            includeHiddenNodes: true,
          });
        }, 100);

        return updatedNodes;
      });
    },
    [setNodes, setEdges, fitView]
  );

  // Set initial viewport
  useEffect(() => {
    if (nodes.length > 0 && isInitialLoad.current) {
      isInitialLoad.current = false;
      // Set a wider initial viewport to accommodate the sidebar
      const initialViewport = {
        x: -200, // Adjust this value to move the view left/right
        y: -100, // Adjust this value to move the view up/down
        zoom: 0.7, // Adjust this value to control initial zoom level
      };
      setViewport(initialViewport, { duration: 0 });
    }
  }, [nodes, setViewport]);

  // Handle window resize with adjusted zoom levels
  useEffect(() => {
    const handleResize = () => {
      if (nodes.length > 0) {
        fitView({
          padding: 0.5, // Increased padding
          duration: 800,
          minZoom: 0.3,
          maxZoom: 0.8, // Reduced max zoom
          includeHiddenNodes: true,
        });
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [nodes, fitView]);

  // Fit view only on initial load
  useEffect(() => {
    if (nodes.length > 0 && isInitialLoad.current) {
      isInitialLoad.current = false;
      // Add a small delay to ensure nodes are rendered
      setTimeout(() => {
        fitView({
          padding: 0.3,
          duration: 800,
          minZoom: 0.3,
          maxZoom: 1.5,
          includeHiddenNodes: true,
        });
      }, 100);
    }
  }, [nodes]);

  console.log("workflowData", workflowData);
  console.log("nodes", nodes);

  useEffect(() => {
    // console.log("workflowData", workflowData);
    // Only run this effect when workflowData changes
    if (workflowData === undefined || workflowData === null) {
      // Still loading or not fetched yet
      return;
    }

    // If workflowData is present, but has no nodes/edges, treat as empty workflow
    const hasNodes =
      Array.isArray(workflowData?.nodes) && workflowData.nodes.length > 0;
    const hasEdges =
      Array.isArray(workflowData?.edges) && workflowData.edges.length > 0;

    if (hasNodes) {
      try {
        // Transform nodes to match React Flow's expected structure
        let transformedNodes =
          workflowData?.nodes?.map((node: any) => {
            if (typeof node.data === "string") {
              node.data = JSON.parse(node.data);
            }
            if (typeof node.data === "object" && node.data !== null) {
              let nodeData: any = {};
              // Check if this is a MediaType node saved as InteractiveMessage
              const isMediaTypeNode =
                node.type === 1 &&
                node.data.interactiveMessage?.mediaType > 1 &&
                node.data.interactiveMessage?.type === 1 &&
                !node.data.interactiveMessage?.list;

              switch (node.type) {
                case 0: // FlowStart
                  nodeData = {
                    triggerType:
                      node.data.flowStart?.entryNodeType === 1
                        ? "newLead"
                        : node.data.flowStart?.entryNodeType === 2
                        ? "keywords"
                        : node.data.flowStart?.entryNodeType === 3
                        ? "statusChange"
                        : node.data.flowStart?.entryNodeType === 4
                        ? "leadProject"
                        : "keywords",
                    leadSource:
                      node.data.flowStart?.leadSource?.map((lead: any) => ({
                        source: lead?.source,
                        subSources: lead?.subSource || [],
                      })) || [],
                    statusChange:
                      node.data.flowStart?.leadStatus?.map((status: any) => ({
                        status: status?.status,
                        subStatus: status?.subStatus || [],
                      })) || [],
                    leadProject: node.data.flowStart?.leadProject,
                    isSaved: true,
                  };
                  break;
                case 1: // InteractiveMessage (including MediaType nodes saved as InteractiveMessage)
                  if (isMediaTypeNode) {
                    // This is a MediaType node, map it back to MediaType structure
                    nodeData = {
                      selectedAction: {
                        nodeType: NodeType.MediaType,
                        value: null,
                      },
                      selectedMediaType:
                        node.data.interactiveMessage?.mediaType || 1,
                      message: node.data.interactiveMessage?.body,
                      mediaUrl: node.data.interactiveMessage?.mediaFile || "",
                      mediaCaption: node.data.interactiveMessage?.header || "",
                      footer: node.data.interactiveMessage?.footer || "",
                      buttons:
                        node.data.interactiveMessage?.buttons?.map(
                          (button: any) => ({
                            id: button.id,
                            text: button.name,
                          })
                        ) || [],
                      variables: {
                        customMessageVariables:
                          node.data.interactiveMessage?.variables?.map(
                            (variable: any) => ({
                              veriable: variable.variable,
                              value: variable.value,
                              fallbackValue: variable.fallbackValue,
                              type: 1,
                            })
                          ) || [],
                        webhookBodyVariables: [],
                      },
                      selectedVariable: null,
                      showSaveUserResponse: false,
                      saveResponseType: "variable",
                      response: "",
                      selectedUserResponse: attributesData.find(
                        (attribute: any) => attribute.id === node.attributeId
                      ),
                      editSelectResponseId: null,
                      deleteSelectResponseId: "",
                      isSaved: true,
                    };
                  } else {
                    // This is a regular InteractiveMessage node
                    nodeData = {
                      selectedAction: {
                        nodeType: NodeType.InteractiveMessage,
                        value: node.data.interactiveMessage.type,
                      },
                      message: node.data.interactiveMessage.body,
                      selectedButtonValue: node.data.interactiveMessage.type,
                      selectedRadioType:
                        node.data.interactiveMessage?.mediaType || 2,
                      headerMediaUrl:
                        node.data.interactiveMessage?.mediaFile || "",
                      headerText: node.data.interactiveMessage?.header || "",
                      footer: node.data.interactiveMessage?.footer || "",
                      buttons:
                        node.data.interactiveMessage.buttons?.map(
                          (button: any) => ({
                            id: button.id,
                            text: button.name,
                          })
                        ) || [],
                      selectedList: node.data.interactiveMessage.list
                        ? {
                            listName:
                              node.data.interactiveMessage.list.buttonText,
                            buttonName:
                              node.data.interactiveMessage.list.sections[0]
                                ?.title,
                            inputs:
                              node.data.interactiveMessage.list.sections[0]?.rows?.map(
                                (row: any) => ({
                                  id: row.id,
                                  title: row.title,
                                  description: row.description,
                                })
                              ) || [],
                          }
                        : null,
                      showSaveUserResponse: false,
                      variables: {
                        customMessageVariables:
                          node.data.interactiveMessage.variables?.map(
                            (variable: any) => ({
                              veriable: variable.variable,
                              value: variable.value,
                              fallbackValue: variable.fallbackValue,
                              type: 1,
                            })
                          ) || [],
                        webhookBodyVariables: [],
                      },
                      selectedVariable: null,
                      saveResponseType: "variable",
                      newVariableName: "",
                      response: "",
                      selectedUserResponse: attributesData.find(
                        (attribute: any) => attribute.id === node.attributeId
                      ),
                      editSelectResponseId: null,
                      isSaved: true,
                    };
                  }
                  break;
                case 2: // Template
                  nodeData = {
                    templateObj: node.data.template,
                    ...node.data,
                    isSaved: true,
                  };
                  delete nodeData.template;
                  break;
                case 3: // HttpRequest
                  nodeData = {
                    webhookTriggerUrl: node.data.httpRequest?.url,
                    webhookTriggerHttpMethod: node.data.httpRequest?.method,
                    webhookTriggerHeader: Object.entries({
                      "Content-Type":
                        node?.data?.httpRequest?.contentType ||
                        "application/json",
                      ...node.data.httpRequest?.headers,
                    }).map(([key, value]) => ({
                      key,
                      value,
                    })),
                    webhookTriggerBody: JSON.parse(
                      node.data.httpRequest?.jsonBody
                    ),
                    variables: {
                      customMessageVariables: [],
                      webhookBodyVariables:
                        node.data.httpRequest?.variableValues?.map(
                          (variable: any) => ({
                            veriable: variable.variable,
                            value: variable.value,
                            fallbackValue: variable.fallbackValue,
                            type: 3,
                          })
                        ) || [],
                    },
                    isSaved: true,
                  };
                  break;
                case 4: // Condition
                  nodeData = {
                    attribute: node.data.condition?.attribute,
                    conditionType: [node.data.condition?.operator],
                    conditionValue: node.data.condition?.value,
                    conditionButtons: node.data.condition?.buttons,
                    isSaved: true,
                  };
                  break;
                // case 5: // MediaType
                //   nodeData = {
                //     selectedAction: {
                //       nodeType: NodeType.MediaType,
                //       value: null,
                //     },
                //     selectedMediaType: node.data.mediaType?.mediaType || 1,
                //     message: node.data.mediaType?.body,
                //     mediaUrl: node.data.mediaType?.mediaUrl,
                //     mediaCaption: node.data.mediaType?.mediaCaption,
                //     footer: node.data.mediaType?.footer,
                //     buttons:
                //       node.data.mediaType?.buttons?.map((button: any) => ({
                //         id: button.id,
                //         text: button.name,
                //       })) || [],
                //     variables: {
                //       customMessageVariables:
                //         node.data.mediaType?.variables?.map(
                //           (variable: any) => ({
                //             veriable: variable.variable,
                //             value: variable.value,
                //             fallbackValue: variable.fallbackValue,
                //             type: 1,
                //           })
                //         ) || [],
                //       webhookBodyVariables: [],
                //     },
                //     selectedVariable: null,
                //     isSaved: true,
                //   };
                //   break;
              }
              return {
                id: node.id,
                type: isMediaTypeNode
                  ? NodeType.MediaType
                  : mapNodeType(node.type),
                position: {
                  x: node.positionX || 0,
                  y: node.positionY || 0,
                },
                data: nodeData,
              };
            }
            return null;
          }) || [];
        // If no nodes exist or no FlowStart node exists, add the initial FlowStart node
        const hasFlowStartNode = transformedNodes.some(
          (node: any) => node.type === NodeType.FlowStart
        );
        if (!hasFlowStartNode) {
          transformedNodes = [
            {
              id: uuidv4(),
              type: NodeType.FlowStart,
              position: { x: 100, y: 100 },
              data: {
                keywords: [], // Initialize with empty array
                leadSource: [], // Initialize with empty array
                statusChange: [], // Initialize with empty array
                triggerType: "keywords",
                isMessagePanelOpen: true,
                isSaved: false,
              },
            },
            ...transformedNodes,
          ];
        }

        // Function to normalize nodes for comparison
        const normalizeNodes = (nodes: any[]) => {
          return nodes.map((node) => ({
            id: node.id,
            type: node.type,
            position: node.position,
            data: node.data,
          }));
        };

        setNodes((prevNodes: any) => {
          const normalizedPrevNodes = normalizeNodes(prevNodes);
          const normalizedTransformedNodes = normalizeNodes(transformedNodes);
          if (
            JSON.stringify(normalizedPrevNodes) !==
            JSON.stringify(normalizedTransformedNodes)
          ) {
            return transformedNodes;
          }
          return prevNodes;
        });

        // Transform edges to match React Flow's expected structure
        const transformedEdges =
          workflowData?.edges?.map((edge: any) => ({
            id: edge.id,
            source: edge.sourceNodeId,
            target: edge.targets[0].targetNodeId,
            sourceHandle: edge.sourceHandle,
            targetHandle: edge.targetHandle,
            type: edge.type,
            markerEnd: {
              type: MarkerType.ArrowClosed,
              width: 20,
              height: 20,
              color: bgColors.green,
            },
          })) || [];

        // Filter out invalid edges
        // transformedEdges = getValidEdges(transformedNodes, transformedEdges);

        setEdges((prevEdges: any) => {
          if (JSON.stringify(prevEdges) !== JSON.stringify(transformedEdges)) {
            return transformedEdges;
          }
          return prevEdges;
        });

        // Fit view after setting nodes
        setTimeout(() => {
          fitView({
            padding: 0.2,
            duration: 800,
            minZoom: 0.3,
            maxZoom: 1.2,
            includeHiddenNodes: true,
          });
        }, 100);
      } catch (error) {
        console.log("error", error);
        setNodes(initialNodes);
        setEdges(initialEdges);
        setTempWorkflowName("");
      }
    }
    if (!hasNodes) {
      setNodes(initialNodes);
    }
    if (!hasEdges) {
      setEdges(initialEdges);
    }
    setTempWorkflowName(workflowData?.name || "");
    setIsWorkflowActive(workflowData?.isActive ?? true);
  }, [workflowData]);

  async function getWorkflowData(id: any) {
    const result = await dispatch(getWorkflowReactflowById(id));
    try {
      if (result?.meta?.requestStatus === "fulfilled") {
        setWorkflowData(result?.payload?.data);

        await fetchWorkflowAllKeywords();
      } else {
        dispatch(
          toastActions.setToaster({
            message: result?.payload?.message || "Failed to fetch workflow",
            type: "error",
          })
        );
      }
    } catch (error: any) {
      console.log("error", error);
      dispatch(
        toastActions.setToaster({
          message: error?.message || "Failed to fetch workflow",
          type: "error",
        })
      );
    }
  }

  useEffect(() => {
    getWorkflowData(id);
  }, [id]);

  useEffect(() => {
    dispatch(getSaveResponseAttribute());
  }, []);

  // useEffect(() => {
  //   fetchFlowstartNodes();
  // }, []);

  async function fetchWorkflowAllKeywords() {
    const response = await dispatch(getWorkflowAllKeywords(id));
    if (response.meta.requestStatus === "fulfilled") {
      // Make sure we're getting an array of keywords
      const keywordsArray = Array.isArray(response.payload?.data)
        ? response.payload?.data
        : [];
      setNodes((prevNodes: any) =>
        prevNodes.map((node: any) => {
          if (node.type === NodeType.FlowStart) {
            return {
              ...node,
              data: {
                ...node.data,
                keywords: keywordsArray,
                isSaved: node.data.isSaved,
              },
            };
          }
          return node;
        })
      );
    } else {
      dispatch(
        toastActions.setToaster({
          message:
            response?.payload?.data?.message ||
            "An error occurred while fetching the keywords",
          type: "error",
        })
      );
    }
  }

  useEffect(() => {
    const data = {
      businessId: userData?.companyId,
    };
    dispatch(getFlowstartNodes(data));
  }, []);

  const toggleArrow = () => {
    setActionsVisible((prevState) => !prevState);
  };

  const dragOutSideRef = useRef(null);

  const onDragStart = (event: React.DragEvent, value: any) => {
    dragOutSideRef.current = value;
    event.dataTransfer.effectAllowed = "move";
  };

  const onDragOver: React.DragEventHandler<HTMLDivElement> = (
    event: React.DragEvent
  ) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  };

  const onDrop: React.DragEventHandler<HTMLDivElement> = (event) => {
    event.preventDefault();
    const displayName = dragOutSideRef.current;

    if (!displayName) return;

    // Check if the drop occurred within the sidebar boundaries
    if (sidebarRef.current) {
      const sidebarRect = sidebarRef.current.getBoundingClientRect();
      if (
        event.clientX >= sidebarRect.left &&
        event.clientX <= sidebarRect.right &&
        event.clientY >= sidebarRect.top &&
        event.clientY <= sidebarRect.bottom
      ) {
        return; // Don't create a node if dropped inside the sidebar
      }
    }

    // Get initial position from cursor
    let position = screenToFlowPosition({
      x: event.clientX,
      y: event.clientY,
    });

    const selectedAction =
      messages.find((message) => message.label === displayName) ||
      actions.find((action) => action.label === displayName);

    if (selectedAction) {
      const newNode: Node = {
        id: uuidv4(),
        type: selectedAction?.nodeType ?? "default",
        position,
        data: {
          keywords: [],
          leadSource: [],
          statusChange: [],
          leadProject: [],
          triggerType: "keywords" as
            | "keywords"
            | "newLead"
            | "statusChange"
            | "leadProject",
          conditionType: [ConditionOperator.Equals],
          conditionButtons: [
            { id: uuidv4(), name: "true" },
            { id: uuidv4(), name: "false" },
          ],
          attribute: "",
          conditionValue: "",
          templateObj: null,
          selectedTemplate: null,
          templateSelectorOpen: false,
          selectedPath: true,
          selectedAction: selectedAction,
          isMessagePanelOpen: true,
          message: "",
          selectedButtonValue: selectedAction.value,
          selectedRadioType: 2,
          buttons:
            selectedAction.value === InteractiveType.Button
              ? [
                  {
                    id: uuidv4(),
                    text: "",
                  },
                ]
              : [],
          selectedList: null,
          showSaveUserResponse: false,
          variables: {
            customMessageVariables: [] as any[],
            webhookBodyVariables: [] as any[],
          },
          saveResponseType: "variable",
          newVariableName: "",
          response: "",
          selectedUserResponse: null,
          editSelectResponseId: null,
          deleteSelectResponseId: "",
          webhookTriggerHttpMethod: "",
          webhookTriggerUrl: "www.example.com",
          webhookTriggerHeader: [
            { key: "Content-Type", value: "application/json" },
          ],
          defaultErrorResponse:
            "We are sorry. Unable to process your request at this time. Please try again later.",
          webhookTriggerBody: "{}",
          regards: "",
          // MediaType specific fields
          selectedMediaType: 1, // Default to NONE
          mediaUrl: "",
          mediaCaption: "",
          footer: "",
          isSaved: false,
        },
      };
      setNodes((prevNodes: any) => [...prevNodes, newNode]);

      // Wait for the node to be rendered
      setTimeout(() => {
        // Get the current viewport
        const viewport = getViewport();

        // Calculate a wider view to include the sidebar
        const extendedView = {
          x: viewport.x + 100, // Adjusted to show more of the sidebar
          y: viewport.y,
          zoom: 0.7, // Set a consistent zoom level
        };

        // First set the view to include the sidebar
        setViewport(extendedView, { duration: 0 });

        // Then fit the view to the node with adjusted padding
        fitView({
          nodes: [newNode],
          duration: 800,
          padding: 0.4, // Increased padding
          minZoom: 0.3,
          maxZoom: 1.2, // Reduced max zoom
          includeHiddenNodes: true,
        });
      }, 300);
    }
  };

  const onConnect = useCallback(
    (connection: Connection) => {
      setEdges((prevEdges) => {
        // Check if the connection is from a button handle
        const isButtonConnection =
          connection.sourceHandle?.startsWith("right-listId") ||
          connection.sourceHandle?.startsWith("right-buttonId-") ||
          connection.sourceHandle?.startsWith("right-conditionButtonId-");

        // Check if the connection is from a node handle
        const isNodeConnection =
          connection.sourceHandle?.startsWith("right-nodeId-");

        // If neither button nor node connection, reject the connection
        if (!isButtonConnection && !isNodeConnection) {
          return prevEdges;
        }

        // Get all existing connections from the same source node
        const existingConnections = prevEdges.filter(
          (edge: any) => edge.source === connection.source
        );

        // Check if there are any existing connections of the other type
        const hasOtherTypeConnections = existingConnections.some(
          (edge: any) => {
            if (isButtonConnection) {
              // If trying to add button connection, check for node connections
              return edge.sourceHandle?.startsWith("right-nodeId-");
            } else {
              // If trying to add node connection, check for button connections
              return (
                edge.sourceHandle?.startsWith("right-listId") ||
                edge.sourceHandle?.startsWith("right-buttonId-")
              );
            }
          }
        );

        // If there are connections of the other type, remove them all
        let updatedEdges = prevEdges;
        if (hasOtherTypeConnections) {
          updatedEdges = prevEdges.filter((edge: any) => {
            // Keep connections that are not from the same source node
            if (edge.source !== connection.source) return true;

            // For the same source node, keep only connections of the same type as the new connection
            if (isButtonConnection) {
              return (
                edge.sourceHandle?.startsWith("right-listId") ||
                edge.sourceHandle?.startsWith("right-buttonId-") ||
                edge.sourceHandle?.startsWith("right-conditionButtonId-")
              );
            } else {
              return edge.sourceHandle?.startsWith("right-nodeId-");
            }
          });
        }

        // For node connections, remove any existing node connection
        if (isNodeConnection) {
          updatedEdges = updatedEdges.filter((edge: any) => {
            // Keep connections that are not from the same source node
            if (edge.source !== connection.source) return true;
            // Remove any node connection
            return !edge.sourceHandle?.startsWith("right-nodeId-");
          });
        }

        // For button connections, remove any existing connection from the same button
        if (isButtonConnection) {
          updatedEdges = updatedEdges.filter((edge: any) => {
            // Keep connections that are not from the same source node
            if (edge.source !== connection.source) return true;
            // Keep connections from other buttons
            return edge.sourceHandle !== connection.sourceHandle;
          });
        }

        const newEdge = {
          ...connection,
          type: "customEdge",
          id: uuidv4(),
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 20,
            height: 20,
            color: `${bgColors.green}`,
          },
        };

        // Create the new edges array with the new edge added
        const newEdges = addEdge(newEdge, updatedEdges);

        // After adding the edge, validate the source node
        // This is done outside the setEdges callback to avoid nested state updates
        setTimeout(() => {
          if (connection.source) {
            const sourceNode = nodes.find(
              (node) => node.id === connection.source
            );
            if (sourceNode && sourceNode.data.isValid === false) {
              // Only validate nodes that were previously marked as invalid
              const { isValid } = validateSingleNode(
                connection.source,
                newEdges
              );

              // Update the node's validation status if it's now valid
              if (isValid) {
                setNodes((prevNodes) =>
                  prevNodes.map((node) => {
                    if (node.id === connection.source) {
                      return {
                        ...node,
                        data: {
                          ...node.data,
                          isValid: true,
                        },
                      };
                    }
                    return node;
                  })
                );
              }
            }
          }
        }, 0);

        return newEdges;
      });
    },
    [nodes]
  );

  const isValidConnection = (connection: Edge | Connection) => {
    const { source, target } = connection;
    if (source === target) return false;
    return true;
  };

  const edgeReconnectSuccessfull = useRef(false);

  const onReconnectStart = () => {
    edgeReconnectSuccessfull.current = false;
  };

  const onReconnect = useCallback(
    (oldEdge: Edge, newConnection: Connection) => {
      edgeReconnectSuccessfull.current = true;
      setEdges((prevEdge: any) => {
        const newEdges = reconnectEdge(oldEdge, newConnection, prevEdge);

        // After reconnecting the edge, validate the source node
        setTimeout(() => {
          if (newConnection.source) {
            const sourceNode = nodes.find(
              (node) => node.id === newConnection.source
            );
            if (sourceNode && sourceNode.data.isValid === false) {
              // Only validate nodes that were previously marked as invalid
              const { isValid } = validateSingleNode(
                newConnection.source,
                newEdges
              );

              // Update the node's validation status if it's now valid
              if (isValid) {
                setNodes((prevNodes) =>
                  prevNodes.map((node) => {
                    if (node.id === newConnection.source) {
                      return {
                        ...node,
                        data: {
                          ...node.data,
                          isValid: true,
                        },
                      };
                    }
                    return node;
                  })
                );
              }
            }
          }
        }, 0);

        return newEdges;
      });
    },
    [nodes]
  );

  const onReconnectEnd = (_: MouseEvent | TouchEvent, edge: Edge) => {
    if (!edgeReconnectSuccessfull.current) {
      setEdges((prevEdges: any) =>
        prevEdges?.filter((prevEdge: any) => prevEdge?.id !== edge?.id)
      );
    }
  };

  // Add this function before createNode
  const getNodeTypeNumber = (type: string): number => {
    switch (type) {
      case NodeType.FlowStart:
        return 0;
      case NodeType.InteractiveMessage:
        return 1;
      case NodeType.Template:
        return 2;
      case NodeType.HttpRequest:
        return 3;
      case NodeType.Condition:
        return 4;
      case NodeType.MediaType:
        return 5;
      default:
        return -1;
    }
  };

  // Add this function before the useEffect
  const mapNodeType = (type: number | string): string => {
    switch (Number(type)) {
      case 0:
        return NodeType.FlowStart;
      case 1:
        return NodeType.InteractiveMessage;
      case 2:
        return NodeType.Template;
      case 3:
        return NodeType.HttpRequest;
      case 4:
        return NodeType.Condition;
      case 5:
        return NodeType.MediaType;
      default:
        return NodeType.FlowStart;
    }
  };

  function createNode(node: any, type: NodeType, positionX = 0, positionY = 0) {
    return {
      id: node?.id,
      type:
        type === NodeType.MediaType
          ? getNodeTypeNumber(NodeType.InteractiveMessage)
          : getNodeTypeNumber(node?.type),
      data: {
        flowStart:
          type === NodeType.FlowStart
            ? {
                entryNodeType:
                  node?.data?.triggerType === "keywords"
                    ? 2
                    : node?.data?.triggerType === "newLead"
                    ? 1
                    : node?.data?.triggerType === "statusChange"
                    ? 3
                    : node?.data?.triggerType === "leadProject"
                    ? 4
                    : 2,
                leadSource:
                  node?.data?.leadSource?.map((lead: any) => ({
                    source: lead?.source,
                    subSource: lead?.subSources,
                  })) || [],
                leadStatus:
                  node?.data?.statusChange?.map((statusWithSubstatus: any) => ({
                    status: statusWithSubstatus?.status,
                    subStatus: statusWithSubstatus?.subStatus || [],
                  })) || [],
                leadProject: node?.data?.leadProject || [],
              }
            : null,
        interactiveMessage:
          type === NodeType.MediaType
            ? {
                type: 1, // MediaType always has buttons
                mediaType: node?.data?.selectedMediaType || 1,
                body: node?.data?.message,
                mediaFile: node?.data?.mediaUrl || "",
                header: node?.data?.mediaCaption || "",
                footer: node?.data?.footer || "",
                buttons:
                  node?.data?.buttons?.map((button: any) => ({
                    id: button?.id,
                    name: button?.text,
                  })) || [],
                variables:
                  node?.data?.variables?.customMessageVariables?.map(
                    (variable: any) => ({
                      variable: variable?.veriable,
                      value: variable?.value,
                      fallbackValue: variable?.fallbackValue,
                      type: 1,
                    })
                  ) || [],
                list: null,
              }
            : type === NodeType.InteractiveMessage
            ? {
                type: node?.data?.selectedList
                  ? 2
                  : node?.data?.buttons && node?.data?.buttons.length > 0
                  ? 1
                  : 0,
                mediaType: node?.data?.selectedRadioType || 1,
                body: node?.data?.message,
                mediaFile: node?.data?.headerMediaUrl || "",
                headerText: node?.data?.headerText || "",
                footer: node?.data?.footer || "",
                buttons:
                  node?.data?.buttons?.map((button: any) => ({
                    id: button?.id,
                    name: button?.text,
                  })) || [],
                variables:
                  node?.data?.variables?.customMessageVariables?.map(
                    (variable: any) => ({
                      variable: variable?.veriable,
                      value: variable?.value,
                      fallbackValue: variable?.fallbackValue,
                      type: 1,
                    })
                  ) || [],
                list: node?.data?.selectedList
                  ? {
                      buttonText: node?.data?.selectedList?.listName,
                      sections: [
                        {
                          title: node?.data?.selectedList?.buttonName,
                          rows:
                            node?.data?.selectedList?.inputs?.map(
                              (input: any) => ({
                                id: input?.id,
                                title: input?.title,
                                description: input?.description,
                              })
                            ) || [],
                        },
                      ],
                    }
                  : null,
              }
            : null,

        httpRequest:
          type === NodeType.HttpRequest
            ? {
                url: node?.data?.webhookTriggerUrl,
                method: node?.data?.webhookTriggerHttpMethod,
                contentType:
                  node?.data?.webhookTriggerHeader?.find(
                    (header: any) =>
                      header.key === "Content-Type" || header.key === "Accept"
                  )?.value || "application/json",
                headers:
                  node?.data?.webhookTriggerHeader
                    ?.filter(
                      (header: any) =>
                        header.key !== "Content-Type" || header.key !== "Accept"
                    )
                    ?.reduce((acc: any, header: any) => {
                      acc[header.key] = header.value;
                      return acc;
                    }, {}) || {},
                jsonBody:
                  JSON.stringify(node?.data?.webhookTriggerBody) || "{}",
                variableValues:
                  node?.data?.variables?.webhookBodyVariables?.map(
                    (variable: any) => ({
                      variable: variable?.veriable,
                      value: variable?.value,
                      fallbackValue: variable?.fallbackValue,
                      type: 3,
                    })
                  ) || [],
              }
            : null,
        template:
          type === NodeType.Template
            ? {
                templateId: node?.data?.templateObj?.templateId || "",
                templateName: node?.data?.templateObj?.templateName || "",
                bodyVariableValues: node?.data?.templateObj?.variables?.length
                  ? node?.data?.templateObj?.variables
                      ?.filter((variable: any) => variable.type === "body")
                      .map((variable: any) => variable.value)
                  : node?.data?.templateObj?.leadratVariables?.length
                  ? node?.data?.templateObj?.leadratVariables
                      ?.filter((variable: any) => variable.type === "body")
                      .map((variable: any) => variable.value)
                  : [],
                headerValue: node?.data?.templateObj?.variables?.length
                  ? node?.data?.templateObj?.variables?.find(
                      (variable: any) => variable.type === "header"
                    )?.value
                  : node?.data?.templateObj?.leadratVariables?.length
                  ? node?.data?.templateObj?.leadratVariables?.find(
                      (variable: any) => variable.type === "header"
                    )?.value
                  : "",
              }
            : null,
        condition:
          type === NodeType.Condition
            ? {
                attribute: node?.data?.attribute,
                operator: Array.isArray(node?.data?.conditionType)
                  ? Number(node?.data?.conditionType[0])
                  : Number(node?.data?.conditionType),
                value: node?.data?.conditionValue,
                buttons:
                  node?.data?.conditionButtons?.map((button: any) => ({
                    id: button?.id,
                    name: button?.name,
                  })) || [],
              }
            : null,
      },
      positionX,
      positionY,
      attributeId:
        node?.data?.selectedAction?.nodeType === NodeType.InteractiveMessage ||
        NodeType.MediaType
          ? node?.data?.selectedUserResponse?.id
          : null,
    };
  }

  function createEdge(edge: any, nodes: any[]) {
    const sourceNode = nodes?.find((node) => node?.id === edge?.source);
    const sourceButtonId = edge?.sourceHandle.split("-").slice(2).join("-");

    const buttonName = sourceNode?.data?.interactiveMessage?.buttons?.find(
      (button: any) => button?.id === sourceButtonId
    )?.name;

    const listButtonName =
      sourceNode?.data?.interactiveMessage?.list?.sections[0]?.rows?.find(
        (button: any) => button?.id === sourceButtonId
      )?.title;

    const conditionButtonName = sourceNode?.data?.condition?.buttons?.find(
      (button: any) => button?.id === sourceButtonId
    )?.name;

    // For MediaType nodes, get button name from interactiveMessage structure
    const mediaTypeButtonName =
      sourceNode?.type === NodeType.MediaType
        ? sourceNode?.data?.buttons?.find(
            (button: any) => button?.id === sourceButtonId
          )?.text
        : null;

    const listArray = sourceNode?.data?.interactiveMessage?.list;
    const result: any = {
      id: edge?.id, // Include the edge ID
      sourceNodeId: edge?.source,
      sourceHandle: edge?.sourceHandle,
      targetHandle: edge?.targetHandle,
      type: edge?.type,
      targets: [
        {
          targetNodeId: edge?.target,
          condition:
            (sourceNode?.type === NodeType.InteractiveMessage ||
              sourceNode?.type === 1) &&
            sourceNode?.data?.interactiveMessage?.buttons?.length > 0
              ? `Name == ${buttonName}`
              : (sourceNode?.type === NodeType.InteractiveMessage ||
                  sourceNode?.type === 1) &&
                listArray?.sections[0]?.rows?.length > 0
              ? `Name == '${listButtonName}'`
              : (sourceNode?.type === NodeType.Condition ||
                  sourceNode?.type === 4) &&
                sourceNode?.data?.condition?.buttons?.length > 0 &&
                conditionButtonName !== "false"
              ? `Name == ${conditionButtonName}`
              : (sourceNode?.type === NodeType.MediaType ||
                  sourceNode?.type === 5) &&
                sourceNode?.data?.buttons?.length > 0
              ? `Name == ${mediaTypeButtonName}`
              : null,
        },
      ],
    };
    return result;
  }

  function cleanObject(obj: any): any {
    if (Array.isArray(obj)) {
      const cleanedArray = obj
        .map(cleanObject)
        .filter(
          (item) =>
            item !== null &&
            item !== undefined &&
            (typeof item !== "object" || Object.keys(item).length > 0)
        );
      return cleanedArray.length ? cleanedArray : undefined;
    }

    if (typeof obj === "object" && obj !== null) {
      const cleanedObj: any = {};
      Object.entries(obj).forEach(([key, value]) => {
        const cleanedValue = cleanObject(value);
        const isEmptyObject =
          typeof cleanedValue === "object" &&
          cleanedValue &&
          !Array.isArray(cleanedValue) &&
          Object.keys(cleanedValue).length === 0;

        if (
          cleanedValue !== undefined &&
          cleanedValue !== null &&
          cleanedValue !== "" &&
          !(Array.isArray(cleanedValue) && cleanedValue.length === 0) &&
          !isEmptyObject
        ) {
          cleanedObj[key] = cleanedValue;
        }
      });
      return Object.keys(cleanedObj).length ? cleanedObj : undefined;
    }

    return obj;
  }

  // Validate a single node in the workflow
  const validateSingleNode = (nodeId: string, currentEdges: Edge[]) => {
    // Find the node to validate
    const node = nodes.find((n) => n.id === nodeId);
    console.log("node", node);
    if (!node) return { isValid: true, validationErrors: [] };

    // Skip validation for FlowStartNode
    if (node.type === NodeType.FlowStart) {
      return { isValid: true, validationErrors: [] };
    }

    let isValid = true;
    const validationErrors = [];

    // Validate InteractiveMessage nodes
    if (node.type === NodeType.InteractiveMessage) {
      // Check if message is empty
      if (!node.data.message) {
        validationErrors.push(
          `Interactive Message: Message content cannot be empty`
        );
        isValid = false;
      }
      // Check if custom message variables are empty and fallback value is not provided
      if (node.data.variables?.customMessageVariables?.length > 0) {
        const customMessageVariables =
          node.data.variables?.customMessageVariables;
        customMessageVariables?.forEach((variable: any) => {
          if (!variable.value || variable.value.trim() === "") {
            validationErrors.push(
              `Interactive Message: variable value cannot be empty`
            );
            isValid = false;
          } else if (
            !variable.fallbackValue ||
            variable.fallbackValue.trim() === ""
          ) {
            validationErrors.push(
              `Interactive Message: variable fallback value cannot be empty`
            );
            isValid = false;
          }
        });
      }

      // Check if buttons are empty when button type is selected
      if (
        node.data.selectedButtonValue === InteractiveType.Button &&
        node.data.buttons &&
        node.data.buttons.length > 0
      ) {
        const emptyButtons = node.data.buttons.filter(
          (button: any) => !button.text
        );
        if (emptyButtons.length > 0) {
          validationErrors.push(
            `Interactive Message: Button text cannot be empty`
          );
          isValid = false;
        }

        // Check if buttons are connected to other nodes or if the node itself is connected
        const connectedButtons = currentEdges.filter(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle &&
            edge.sourceHandle.startsWith("right-buttonId-")
        );

        // Check if the node itself is directly connected
        const isNodeDirectlyConnected = currentEdges.some(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle === `right-nodeId-${node.id}`
        );

        // Only validate button connections if the node itself is not connected
        if (
          connectedButtons.length < node.data.buttons.length &&
          !isNodeDirectlyConnected
        ) {
          validationErrors.push(
            `Interactive Message: Either connect all buttons to other nodes or connect this node directly`
          );
          isValid = false;
        }
      }

      // Check if list items are empty when list type is selected
      if (
        node.data.selectedButtonValue === InteractiveType.List &&
        node.data.selectedList &&
        node.data.selectedList.inputs &&
        node.data.selectedList.inputs.length > 0
      ) {
        const emptyListItems = node.data.selectedList.inputs.filter(
          (item: any) => !item.title
        );
        if (emptyListItems.length > 0) {
          validationErrors.push(
            `Interactive Message: List item title cannot be empty`
          );
          isValid = false;
        }

        // Check if list items are connected to other nodes
        const connectedListItems = currentEdges.filter(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle &&
            edge.sourceHandle.startsWith("right-listId-")
        );

        // Check if the node itself is directly connected
        const isNodeDirectlyConnected = currentEdges.some(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle === `right-nodeId-${node.id}`
        );

        // Only validate list item connections if the node itself is not connected
        if (
          connectedListItems.length < node.data.selectedList.inputs.length &&
          !isNodeDirectlyConnected
        ) {
          validationErrors.push(
            `Interactive Message: Either connect all list items to other nodes or connect this node directly`
          );
          isValid = false;
        }
      }
    }

    // Validate Template nodes
    if (node.type === NodeType.Template) {
      if (!node.data.templateObj || !node.data.templateObj.templateId) {
        validationErrors.push(`Template Node: A template must be selected`);
        isValid = false;
      }
    }

    // Validate Condition nodes
    if (node.type === NodeType.Condition) {
      // Check if attribute is selected
      if (!node.data.attribute) {
        validationErrors.push(`Condition Node: An attribute must be selected`);
        isValid = false;
      }

      // Check if condition value is provided
      if (!node.data.conditionValue) {
        validationErrors.push(
          `Condition Node: A condition value must be provided`
        );
        isValid = false;
      }

      // Check if both true and false buttons are connected
      const trueButtonConnected = currentEdges.some(
        (edge) =>
          edge.source === node.id &&
          edge.sourceHandle &&
          edge.sourceHandle.includes("conditionButtonId-") &&
          node.data.conditionButtons.find((btn: any) => btn.name === "true")
            ?.id === edge.sourceHandle?.slice(24)
      );

      const falseButtonConnected = currentEdges.some(
        (edge) =>
          edge.source === node.id &&
          edge.sourceHandle &&
          edge.sourceHandle.includes("conditionButtonId-") &&
          node.data.conditionButtons.find((btn: any) => btn.name === "false")
            ?.id === edge.sourceHandle?.slice(24)
      );

      if (!trueButtonConnected || !falseButtonConnected) {
        validationErrors.push(
          `Condition Node: Both TRUE and FALSE paths must be connected to other nodes`
        );
        isValid = false;
      }
    }

    // Validate HttpRequest (Webhook) nodes
    if (node.type === NodeType.HttpRequest) {
      // Check if URL is provided
      if (!node.data.webhookTriggerUrl) {
        validationErrors.push(`Webhook Node: URL must be provided`);
        isValid = false;
      }

      // Check if HTTP method is selected
      if (!node.data.webhookTriggerHttpMethod) {
        validationErrors.push(`Webhook Node: HTTP method must be selected`);
        isValid = false;
      }

      // Check if body is valid JSON
      try {
        if (node.data.webhookTriggerBody) {
          JSON.parse(node.data.webhookTriggerBody);
        }
      } catch (error) {
        validationErrors.push(`Webhook Node: Body must be valid JSON`);
        isValid = false;
      }

      // Check if variables have all required fields
      if (node.data.variables && node.data.variables.webhookBodyVariables) {
        const invalidVariables =
          node.data.variables.webhookBodyVariables.filter(
            (prop: any) => !prop.veriable || !prop.value || !prop.fallbackValue
          );
        if (invalidVariables.length > 0) {
          validationErrors.push(
            `Webhook Node: All webhook variables must have variable, value, and fallback value`
          );
          isValid = false;
        }
      }
    }

    // Validate MediaType nodes
    if (node.type === NodeType.MediaType) {
      // 1. Media Type validation
      if (node.data.selectedMediaType !== 1) {
        // Not "None"
        if (node.data.selectedMediaType === 2 && !node.data.mediaCaption) {
          // Text type requires caption
          validationErrors.push("Media Type: Text header value is required");
          isValid = false;
        } else if (node.data.selectedMediaType === 3 && !node.data.mediaUrl) {
          // Image type requires URL
          validationErrors.push("Media Type: Image file is required");
          isValid = false;
        } else if (node.data.selectedMediaType === 4 && !node.data.mediaUrl) {
          // Video type requires URL
          validationErrors.push("Media Type: Video file is required");
          isValid = false;
        } else if (node.data.selectedMediaType === 5 && !node.data.mediaUrl) {
          // Document type requires URL
          validationErrors.push("Media Type: Document file is required");
          isValid = false;
        }
      }

      // 2. Message body validation
      if (!node.data.message || node.data.message.trim() === "") {
        validationErrors.push("Media Type: Message content cannot be empty");
        isValid = false;
      }

      // 3. Variables validation
      if (node.data.variables?.customMessageVariables?.length > 0) {
        const invalidVariables =
          node.data.variables.customMessageVariables.filter(
            (variable: any) => !variable.value || !variable.fallbackValue
          );
        if (invalidVariables.length > 0) {
          validationErrors.push(
            "Media Type: All variables must have value and fallback value"
          );
          isValid = false;
        }
      }

      // 4. Button validation
      if (!node.data.buttons || node.data.buttons.length === 0) {
        validationErrors.push("Media Type: At least one button is required");
        isValid = false;
      } else if (node.data.buttons.length > 3) {
        validationErrors.push("Media Type: Maximum 3 buttons are allowed");
        isValid = false;
      } else {
        // Check if all buttons have text
        const emptyButtons = node.data.buttons.filter(
          (button: any) => !button.text || button.text.trim() === ""
        );
        if (emptyButtons.length > 0) {
          validationErrors.push("Media Type: All buttons must have text");
          isValid = false;
        }

        // Check button connections
        const connectedButtons = currentEdges.filter(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle &&
            edge.sourceHandle.startsWith("right-buttonId-")
        );
        const isNodeDirectlyConnected = currentEdges.some(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle === `right-nodeId-${node.id}`
        );

        if (
          connectedButtons.length < node.data.buttons.length &&
          !isNodeDirectlyConnected
        ) {
          validationErrors.push(
            "Media Type: Either connect all buttons to other nodes or connect this node directly"
          );
          isValid = false;
        }
      }
    }

    return { isValid, validationErrors };
  };

  // Validate all nodes in the workflow
  const validateNodes = () => {
    let isValid = true;
    const invalidNodeIds: any = [];
    const validationErrors = [];

    // Check each node for validation errors
    for (const node of nodes) {
      // Skip validation for FlowStartNode
      if (node.type === NodeType.FlowStart) {
        continue;
      }

      const { isValid: nodeIsValid, validationErrors: nodeErrors } =
        validateSingleNode(node.id, edges);

      if (!nodeIsValid) {
        invalidNodeIds.push(node.id);
        validationErrors.push(...nodeErrors);
        isValid = false;
      }
    }

    // Update node validation states
    setNodes((prevNodes) =>
      prevNodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isValid: !invalidNodeIds.includes(node.id),
        },
      }))
    );

    return { isValid, validationErrors };
  };

  const handleSave = async () => {
    setIsSavingWorkflow(true);

    // Validate all nodes
    const { isValid } = validateNodes();

    if (!isValid) {
      // Use a simple error message
      const errorMessage = "Validation Error, Check the Invalid nodes";

      dispatch(
        toastActions.setToaster({
          message: errorMessage,
          type: "error",
        })
      );
      setIsSavingWorkflow(false);
      return;
    }

    const formattedNodes = nodes?.map((node) =>
      createNode(node, node.type as NodeType, node.position.x, node.position.y)
    );

    const formattedEdges = edges?.map((edge) =>
      createEdge(edge, formattedNodes)
    );

    const cleanedNodes = formattedNodes?.map((node) => cleanObject(node));

    const cleanedEdges = formattedEdges?.map((edge) => cleanObject(edge));

    const payload = {
      id: id,

      data: {
        Name: tempWorkflowName || "default name",
        isActive: isWorkflowActive,
        Nodes: cleanedNodes,
        Edges: cleanedEdges,
      },
    };

    let response;
    try {
      response = await dispatch(updateWorkflowReactflow(payload));

      if (response?.meta?.requestStatus === "fulfilled") {
        const companyId = userData?.companyId;

        // Clear local state
        setNodes([]);
        setEdges([]);
        setTempWorkflowName("");

        const payload = {
          keywords: nodes.find((node: any) => node.type === NodeType.FlowStart)
            ?.data?.keywords,
          workflowId: id,
          workflowNodeId: nodes.find(
            (node: any) => node.type === NodeType.FlowStart
          )?.id,
        };
        await dispatch(createKeywordReactflow(payload));
        dispatch(
          toastActions.setToaster({
            message:
              response?.payload?.message || "Workflow updated successfully",
            type: "success",
          })
        );
        // Fetch the updated workflow data before navigating
        dispatch(getAllWorkflowsReactflow(companyId));
        navigate("/automation/workflows");
      } else {
        dispatch(
          toastActions.setToaster({
            message:
              response?.payload?.data?.message || "Failed to update workflow",
            type: "error",
          })
        );
      }
    } catch (error) {
      setIsSavingWorkflow(false);
      dispatch(
        toastActions.setToaster({
          message: "Failed to update workflow",
          type: "error",
        })
      );
    } finally {
      setIsSavingWorkflow(false);
    }
  };

  const handleDiscardChangesBtn = () => {
    setIsCancelPopupOpen(false);
    navigate("/automation/workflows");
  };
  const hanldeSaveChangesBtn = () => {
    setIsCancelPopupOpen(false);
    handleSave();
  };
  function handlePopupClose() {
    setIsCancelPopupOpen(false);
  }

  // Function to add a node when clicked in the sidebar
  const handleNodeClick = (item: any) => {
    // Find the rightmost node position
    let position = { x: 100, y: 100 }; // Default position if no nodes exist

    if (nodes.length > 0) {
      // Find the rightmost node
      let rightmostNode = nodes[0];
      nodes.forEach((node) => {
        if (node.position.x > rightmostNode.position.x) {
          rightmostNode = node;
        }
      });

      // Position new node 400px to the right and 100px below the rightmost node
      position = {
        x: rightmostNode.position.x + 650,
        y: rightmostNode.position.y + 100,
      };
    }

    const selectedAction = item;

    if (selectedAction) {
      const newNode: Node = {
        id: uuidv4(),
        type: selectedAction?.nodeType ?? "default",
        position,
        data: {
          keywords: [],
          leadSource: [],
          statusChange: [],
          triggerType: "keywords" as "keywords" | "newLead" | "statusChange",
          conditionType: [ConditionOperator.Equals],
          conditionButtons: [
            { id: uuidv4(), name: "true" },
            { id: uuidv4(), name: "false" },
          ],
          attribute: "",
          conditionValue: "",
          templateObj: null,
          selectedTemplate: null,
          templateSelectorOpen: false,
          selectedPath: true,
          selectedAction: selectedAction,
          isMessagePanelOpen: true,
          message: "",
          selectedButtonValue: selectedAction.value,
          selectedRadioType: 2,
          buttons:
            selectedAction.value === InteractiveType.Button
              ? [
                  {
                    id: uuidv4(),
                    text: "",
                  },
                ]
              : [],
          selectedList: null,
          showSaveUserResponse: false,
          variables: {
            customMessageVariables: [] as any[],
            webhookBodyVariables: [] as any[],
          },
          saveResponseType: "variable",
          newVariableName: "",
          response: "",
          selectedUserResponse: null,
          editSelectResponseId: null,
          deleteSelectResponseId: "",
          webhookTriggerHttpMethod: "",
          webhookTriggerUrl: "www.example.com",
          webhookTriggerHeader: [
            { key: "Content-Type", value: "application/json" },
          ],
          defaultErrorResponse:
            "We are sorry. Unable to process your request at this time. Please try again later.",
          webhookTriggerBody: "{}",
          regards: "",
          // MediaType specific fields
          selectedMediaType: 1, // Default to NONE
          mediaUrl: "",
          mediaCaption: "",
          footer: "",
          isSaved: false,
        },
      };
      setNodes((prevNodes: any) => [...prevNodes, newNode]);

      // Wait for the node to be rendered
      setTimeout(() => {
        // Get the current viewport
        const viewport = getViewport();

        // Calculate a wider view to include the sidebar
        const extendedView = {
          x: viewport.x - 200, // Adjusted to show more of the sidebar
          y: viewport.y,
          zoom: 0.7, // Set a consistent zoom level
        };

        // First set the view to include the sidebar
        setViewport(extendedView, { duration: 0 });

        // Then fit the view to the node with adjusted padding
        fitView({
          nodes: [newNode],
          duration: 800,
          padding: 0.4, // Increased padding
          minZoom: 0.3,
          maxZoom: 0.8, // Reduced max zoom
          includeHiddenNodes: true,
        });
      }, 300);
    }
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        height: "100vh",
        width: "100%",
        position: "relative",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <WorkflowProvider>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          panOnDrag={true}
          zoomOnScroll={true}
          zoomOnPinch={true}
          minZoom={0.2}
          maxZoom={1.4}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          onConnect={onConnect}
          onDragOver={onDragOver}
          onDrop={onDrop}
          onReconnectStart={onReconnectStart}
          onReconnect={onReconnect}
          onReconnectEnd={onReconnectEnd}
          isValidConnection={isValidConnection}
          connectionLineComponent={ConnectionLine}
          connectionLineStyle={{ stroke: "grey", strokeWidth: 2 }}
          connectionLineType={ConnectionLineType.SmoothStep}
          onNodesDelete={(nodesToDelete) => {
            nodesToDelete.forEach((node) => handleNodeDelete(node.id));
          }}
          fitView
          style={{
            width: "100%",
            height: "100%",
            background: "#f8f8f8",
          }}
        >
          <Panel position="top-right" style={{ margin: "10px" }}>
            <Box
              sx={{
                backgroundColor: "white",
                borderRadius: "8px",
                padding: "8px",
                boxShadow: 1,
                border: "1px solid #e0e0e0",
              }}
            >
              <FormControlLabel
                control={
                  <Switch
                    checked={isWorkflowActive}
                    onChange={(e) => setIsWorkflowActive(e.target.checked)}
                    sx={{
                      "& .MuiSwitch-switchBase.Mui-checked": {
                        color: "#4caf50",
                        "&:hover": {
                          backgroundColor: "rgba(76, 175, 80, 0.04)",
                        },
                      },
                      "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                        {
                          backgroundColor: "#4caf50",
                        },
                    }}
                    size="small"
                  />
                }
                label={
                  <Typography variant="caption">
                    {isWorkflowActive ? "Active" : "Inactive"}
                  </Typography>
                }
                labelPlacement="start"
                sx={{ margin: 0 }}
              />
            </Box>
          </Panel>
          <FlowSidebar
            ref={sidebarRef}
            onToggle={toggleArrow}
            isCollapsed={actionsVisible}
            messages={messages}
            actions={actions}
            onDragStart={onDragStart}
            onNodeClick={handleNodeClick}
            draggable={true}
            onSave={handleSave}
            tempWorkflowName={tempWorkflowName}
            setTempWorkflowName={setTempWorkflowName}
            onCancel={() => setIsCancelPopupOpen(true)}
            workflowData={workflowData}
            isWorkflowActive={isWorkflowActive}
            isSavingWorkflow={isSavingWorkflow}
          />
          <CancelDialogPopup
            open={isCancelPopupOpen}
            handleSave={hanldeSaveChangesBtn}
            handleDiscardChanges={handleDiscardChangesBtn}
            handleClose={handlePopupClose}
            title={tempWorkflowName}
          />
          <Controls
            position="bottom-right"
            style={{
              margin: "10px",
              zIndex: 10,
            }}
          />
          <Background />
        </ReactFlow>
      </WorkflowProvider>
    </Box>
  );
};

export default WorkflowReactFlow;
