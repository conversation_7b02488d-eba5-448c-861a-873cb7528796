/* global process */

import axios from "axios";
import { getStoredTokens } from "../../utils/authUtils";

const INBOX_API_URL = process.env.REACT_APP_BASE_URL;
const INBOX_API_URL_V2 = process.env.REACT_APP_WEB_SOCKET_BASE_URL;

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens.token}` : "";
};

const fetchLatestInboxContacts = (data: any) => {
  return axios({
    url: `${INBOX_API_URL_V2}/api/latestInbox-contacts?BusinessId=${data?.businessId}&UserId=${data?.userId}&page=${data.chatsPageNumber}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.filtering,
  });
};

const fetchInboxContacts = (data: any) => {
  return axios({
    url: `${INBOX_API_URL}/inbox-contacts?BusinessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.filtering,
  });
};

const fetchInboxContactConversations = (data: any) => {
  return axios({
    url: `${INBOX_API_URL}/inbox-contact-conversations?BusinessId=${data?.businessId}&Contact=${data?.contact}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const fetchInboxContactAssignment = (data: any) => {
  return axios({
    url: `${INBOX_API_URL}/assign-chat?BusinessId=${data?.businessId}&AssignUserId=${data?.teamMemberId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: {
      contact: data?.contact,
    },
  });
};

const fetchInboxUserDetails = (data: any) => {
  return axios({
    url: `${INBOX_API_URL}/user-details?BusinessId=${data?.businessId}&Contact=${data?.contactId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const closeChat = (data: any) => {
  return axios({
    url: `${INBOX_API_URL}/close-chat?BusinessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: {
      contact: [data?.contact],
    },
  });
};
const markAsSpam = (data: any) => {
  return axios({
    url: `${INBOX_API_URL}/mark-as-spam?BusinessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: {
      contact: [data?.contact],
    },
  });
};

const metricsData = (businessId: any) => {
  return axios({
    url: `${INBOX_API_URL}/ClientDetails/client-details/${businessId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

export const INBOX_APIS = {
  fetchLatestInboxContacts,
  fetchInboxContacts,
  fetchInboxContactConversations,
  fetchInboxContactAssignment,
  fetchInboxUserDetails,
  closeChat,
  markAsSpam,
  metricsData,
};
