import axios from "axios";

const LEADRAT_QA_BASE_URL = process.env.REACT_APP_LEADRAT_QA_BASE_URL;

const getAllWorkflowStatusByTenantId = (data: any) => {
  return axios({
    url: `${LEADRAT_QA_BASE_URL}/api/v1/status/getallstatusanonymous`,
    method: "GET",
    headers: {
      "accept": "application/json",
      // Authorization: localStorage.getItem("token"),
      tenant: data.tenantId,
    },
  });
};

const getAllWorkflowProjectsByTenantId = (data: any) => {
  return axios({
    url: `${LEADRAT_QA_BASE_URL}/api/v1/project/projectnames`,
    method: "GET",
    headers: {
      "accept": "application/json",
      // Authorization: localStorage.getItem("token"),
      tenant: data?.tenantId,
    },
  });
};

export const LEADRAT_QA_APIS = {
  getAllWorkflowStatusByTenantId,
  getAllWorkflowProjectsByTenantId,
};
