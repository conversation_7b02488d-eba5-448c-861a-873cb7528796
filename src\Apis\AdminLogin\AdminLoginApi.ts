/* global process */

import axios from "axios";
import { getStoredTokens } from "../../utils/authUtils";

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens.token}` : "";
};

/* login api */
const USER_API_URL = process.env.REACT_APP_BASE_URL;
const USER_API_URL_2 = process.env.REACT_APP_WEB_SOCKET_BASE_URL;
const login = (email: string, password: string) => {
  return axios({
    url: `${USER_API_URL_2}/api/Authentication/login`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "no-cache",
    },
    data: JSON.stringify({ email: email, password: password }),
  });
};

const forgotPassword = ({ email }: { email: string }) => {
  return axios({
    url: `${USER_API_URL}/ForgotPassword/send-otp`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: JSON.stringify({ email: email }),
  });
};

const verifyOtp = ({ otp, email }: { otp: string; email: string }) => {
  return axios({
    url: `${USER_API_URL}/ForgotPassword/verify-otp`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: JSON.stringify({ otp: otp, email: email }),
  });
};

const updatePassword = ({
  email,
  password,
  confirmPassword,
}: {
  email: string;
  password: string;
  confirmPassword: string;
}) => {
  return axios({
    url: `${USER_API_URL}/ForgotPassword/update-password`,
    method: "POST",
    headers: {
      // "Accept": "/",
      "Content-Type": "application/json",
    },
    data: JSON.stringify({
      email: email,
      newPassword: password,
      confirmPassword: confirmPassword,
    }),
  });
};

const refreshToken = (data: any) => {
  return axios({
    url: `${USER_API_URL_2}/api/Authentication/refresh-token?token=${data?.token}&refreshToken=${data?.refreshToken}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  }).catch(error => {
    console.error("[AdminLoginApi] Token refresh error:", error);
    throw error;
  });
};

export const ADMIN_LOGIN_API = {
  login,
  forgotPassword,
  verifyOtp,
  updatePassword,
  refreshToken,
};
