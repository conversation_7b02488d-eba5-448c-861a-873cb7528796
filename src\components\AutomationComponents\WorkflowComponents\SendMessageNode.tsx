import { Box } from "@mui/system";
import { useEffect, useState, useCallback } from "react";
import { NodeProps, Position, useReactFlow, Edge } from "reactflow";
import { bgColors } from "../../../utils/bgColors";
import { Button, IconButton, Paper, Typography } from "@mui/material";
import MessageEditorPanel from "./MessageEditorPanel";
import CustomHandle from "./CustomHandle";
import { parseTextToHtml } from "../../../utils/functions";
import { convertFromRaw } from "draft-js";
import { InteractiveType } from "./enums";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import { v4 as uuidv4 } from "uuid";
import { useWorkflow } from "../../../contexts/WorkflowContext";

export interface ButtonListType {
  id: string;
  companyId: string;
  userId: string;
  listName: string;
  buttonName: string;
  inputs: { title: string; description: string }[];
}

interface SendMessageNodeProps extends NodeProps {
  data: {
    selectedAction: any;
    isMessagePanelOpen: boolean;
    message: string;
    selectedButtonValue: InteractiveType | string;
    selectedRadioType: number;
    buttons: any[];
    selectedList: any;
    showSaveUserResponse: boolean;
    variables: any;
    selectedVariable: any;
    saveResponseType: string;
    response: any;
    selectedUserResponse: any;
    editSelectResponseId: string;
    deleteSelectResponseId: string;
    isValid?: boolean;
  };
}

const SendMessageNode = ({ data, id }: SendMessageNodeProps) => {
  const {
    selectedAction,
    isMessagePanelOpen,
    message,
    selectedButtonValue: rawSelectedButtonValue,
    selectedRadioType,
    buttons,
    selectedList,
    showSaveUserResponse,
    variables: initialVariables,
    selectedVariable,
    saveResponseType,
    response,
    selectedUserResponse,
    editSelectResponseId,
    deleteSelectResponseId,
  } = data;
  const { handleNodeDelete } = useWorkflow();
  const [localVariables, setLocalVariables] = useState(initialVariables);

  useEffect(() => {
    // Update localVariables whenever initialVariables changes
    if (initialVariables) {
      setLocalVariables(initialVariables);
    }
  }, [initialVariables]);

  // Ensure selectedButtonValue is always a valid value
  const selectedButtonValue =
    rawSelectedButtonValue === null || rawSelectedButtonValue === undefined
      ? ""
      : rawSelectedButtonValue;

  const { setNodes, getEdges, getNodes } = useReactFlow();

  // Get the current edges for validation
  const edges = getEdges();

  const updateNodeData = (updateFn: (data: any) => any) => {
    setNodes((nds: any[]) =>
      nds.map((node: any) => {
        if (node.id === id) {
          const updatedData = updateFn(node.data);

          // Only validate if the node has already been marked as invalid
          // This ensures validation only runs after the Save button has been clicked
          if (updatedData.isValid === false) {
            // Validate the node data to determine if all errors are resolved
            // For message nodes, we need to check if message is not empty
            const isMessageProvided = !!updatedData.message;

            // Check if buttons have text when button type is selected
            let areButtonsValid = true;
            if (
              updatedData.selectedButtonValue === InteractiveType.Button &&
              updatedData.buttons &&
              updatedData.buttons.length > 0
            ) {
              areButtonsValid = !updatedData.buttons.some(
                (button: any) => !button.text
              );
              const connectedButtons = edges.filter(
                (edge) =>
                  edge.source === id &&
                  edge.sourceHandle &&
                  edge.sourceHandle.startsWith("right-buttonId-")
              );
              const isNodeConnected = edges.some(
                (edge) =>
                  edge.source === id &&
                  edge.sourceHandle === `right-nodeId-${id}`
              );

              areButtonsValid =
                areButtonsValid &&
                (connectedButtons.length === updatedData.buttons.length ||
                  isNodeConnected);
            }

            // Check if list items have titles when list type is selected
            let areListItemsValid = true;
            if (
              updatedData.selectedButtonValue === InteractiveType.List &&
              updatedData.selectedList?.inputs
            ) {
              areListItemsValid = !updatedData.selectedList.inputs.some(
                (item: any) => !item.title
              );
              const connectedListItems = edges.filter(
                (edge) =>
                  edge.source === id &&
                  edge.sourceHandle &&
                  edge.sourceHandle.startsWith("right-listId-")
              );
              const isNodeConnected = edges.some(
                (edge) =>
                  edge.source === id &&
                  edge.sourceHandle === `right-nodeId-${id}`
              );

              areListItemsValid =
                areListItemsValid &&
                (connectedListItems.length ===
                  updatedData.selectedList.inputs.length ||
                  isNodeConnected);
            }

            // Only set isValid to true if all validation checks pass
            if (isMessageProvided && areButtonsValid && areListItemsValid) {
              updatedData.isValid = true;
            }
          }

          return { ...node, data: updatedData };
        }
        return node;
      })
    );
  };

  const handleEdit = () => {
    updateNodeData((data) => ({
      ...data,
      isMessagePanelOpen: !data.isMessagePanelOpen,
    }));
  };

  const handleDelete = () => {
    handleNodeDelete(id);
  };

  const handleMessage = (message: string) => {
    updateNodeData((data) => ({
      ...data,
      message,
    }));
  };

  const handleSelectedButtonValue = (buttonValue: string) => {
    updateNodeData((data) => ({
      ...data,
      selectedButtonValue: buttonValue,
    }));
  };

  const handleRadioType = (value: number) => {
    updateNodeData((data) => ({
      ...data,
      selectedRadioType: value,
    }));
  };

  const handleButtons = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      buttons: [...(data.buttons || []), value],
    }));
  };

  const handleVariablesChange = useCallback(
    (newVariable: any, actionType: string) => {
      setLocalVariables((prevState: any) => {
        // Initialize the structure if it doesn't exist
        const updatedCustomMessageVariables =
          prevState?.customMessageVariables || [];

        // Create the new variable structure with empty arrays
        const newVariableStructure: {
          normalVariables: any[];
          leadratVariables: any[];
        } = {
          normalVariables: [],
          leadratVariables: [],
        };

        // If there's no existing structure, add the new one
        if (updatedCustomMessageVariables.length === 0) {
          // Add the new variable to the appropriate array
          if (actionType === "addVariable") {
            newVariableStructure.normalVariables = Array.isArray(newVariable)
              ? newVariable
              : [newVariable];
          } else if (actionType === "leadratVariable") {
            newVariableStructure.leadratVariables = Array.isArray(newVariable)
              ? newVariable
              : [newVariable];
          }
          updatedCustomMessageVariables.push(newVariableStructure);
        } else {
          // For existing structure, completely replace the arrays instead of merging
          updatedCustomMessageVariables[0] = {
            normalVariables:
              actionType === "addVariable"
                ? Array.isArray(newVariable)
                  ? newVariable
                  : [newVariable]
                : [],
            leadratVariables:
              actionType === "leadratVariable"
                ? Array.isArray(newVariable)
                  ? newVariable
                  : [newVariable]
                : [],
          };
        }

        // Only update node data if there are actual changes
        const newState = {
          ...prevState,
          customMessageVariables: updatedCustomMessageVariables,
        };

        // Compare with current state to prevent unnecessary updates
        if (JSON.stringify(newState) !== JSON.stringify(prevState)) {
          // Use setTimeout to break the synchronous update cycle
          setTimeout(() => {
            updateNodeData((data) => ({
              ...data,
              variables: {
                ...(data.variables || {}),
                customMessageVariables: updatedCustomMessageVariables,
              },
            }));
          }, 0);
        }

        return newState;
      });
    },
    [updateNodeData]
  );

  const handleVariablesValueChange = (
    newVariableObj: any,
    actionType: string
  ) => {
    setLocalVariables((prevState: any) => {
      const updatedCustomMessageVariables =
        prevState?.customMessageVariables || [];

      if (updatedCustomMessageVariables.length === 0) {
        return prevState;
      }

      // Update the appropriate array based on actionType
      const updatedVariables = {
        normalVariables:
          actionType === "addVariable"
            ? updatedCustomMessageVariables[0].normalVariables.map(
                (item: any) =>
                  item.veriable === newVariableObj.veriable
                    ? { ...item, value: newVariableObj.value || "" }
                    : item
              )
            : updatedCustomMessageVariables[0].normalVariables,
        leadratVariables:
          actionType === "leadratVariable"
            ? updatedCustomMessageVariables[0].leadratVariables.map(
                (item: any) =>
                  item.veriable === newVariableObj.veriable
                    ? { ...item, value: newVariableObj.value || "" }
                    : item
              )
            : updatedCustomMessageVariables[0].leadratVariables,
      };

      const newState = {
        ...prevState,
        customMessageVariables: [updatedVariables],
      };

      // Update node data with the new state
      setTimeout(() => {
        updateNodeData((data) => ({
          ...data,
          variables: {
            ...(data.variables || {}),
            customMessageVariables: [updatedVariables],
          },
        }));
      }, 0);

      return newState;
    });
  };

  const handleVariablesFallbackValueChange = (
    newVariableObj: any,
    actionType: string
  ) => {
    setLocalVariables((prevState: any) => {
      const updatedCustomMessageVariables =
        prevState?.customMessageVariables || [];

      if (updatedCustomMessageVariables.length === 0) {
        return prevState;
      }
      const updatedVariables = {
        normalVariables:
          actionType === "addVariable"
            ? updatedCustomMessageVariables[0].normalVariables.map(
                (item: any) =>
                  item.veriable === newVariableObj.veriable
                    ? {
                        ...item,
                        fallbackValue: newVariableObj.fallbackValue || "",
                      }
                    : item
              )
            : updatedCustomMessageVariables[0].normalVariables,
        leadratVariables:
          actionType === "leadratVariable"
            ? updatedCustomMessageVariables[0].leadratVariables.map(
                (item: any) =>
                  item.veriable === newVariableObj.veriable
                    ? {
                        ...item,
                        fallbackValue: newVariableObj.fallbackValue || "",
                      }
                    : item
              )
            : updatedCustomMessageVariables[0].leadratVariables,
      };

      const newState = {
        ...prevState,
        customMessageVariables: [updatedVariables],
      };
      setTimeout(() => {
        updateNodeData((data) => ({
          ...data,
          variables: {
            ...(data.variables || {}),
            customMessageVariables: [updatedVariables],
          },
        }));
      }, 0);

      return newState;
    });
  };

  const handleButtonText = (buttonId: string, value: string) => {
    updateNodeData((data) => ({
      ...data,
      buttons: data.buttons.map((button: any) =>
        button.id === buttonId ? { ...button, text: value } : button
      ),
    }));
  };

  const handleRemoveButton = (buttonId: string) => {
    updateNodeData((data) => ({
      ...data,
      buttons: data.buttons.filter((button: any) => button.id !== buttonId),
    }));
  };

  const handleSelectedList = (value: any) => {
    // Add UUIDs to each input in the list
    const listWithIds = {
      ...value,
      inputs: value.inputs.map((input: any) => ({
        ...input,
        id: uuidv4(),
      })),
    };

    updateNodeData((data) => ({
      ...data,
      selectedList: listWithIds,
    }));
  };

  const handleShowSaveUserResponse = () => {
    updateNodeData((data) => ({
      ...data,
      showSaveUserResponse: !data?.showSaveUserResponse,
    }));
  };

  const handleSaveResponseType = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      saveResponseType: value,
    }));
  };

  const handleSelectedResponse = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      selectResponse: value,
    }));
  };
  const handleResponse = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      response: value,
    }));
  };
  const handleEditSelectResponseId = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      editSelectResponseId: value,
    }));
  };

  const handleDeleteSelectResponseId = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      deleteSelectResponseId: value,
    }));
  };

  const handleSelectedVariable = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      selectedVariable: value,
    }));
  };

  const formatContentForDisplay = (rawContent: string) => {
    try {
      const contentState = convertFromRaw(JSON.parse(rawContent));
      return contentState.getPlainText();
    } catch (error) {
      return rawContent;
    }
  };

  // Helper function to check if a node is directly connected to another node
  const isNodeConnected = (nodeId: string, allEdges: Edge[]) => {
    return allEdges.some(
      (edge) =>
        edge.source === nodeId && edge.sourceHandle === `right-nodeId-${nodeId}`
    );
  };

  const isButtonConnected = (buttonId: string, allEdges: Edge[]) => {
    return allEdges.some(
      (edge) =>
        edge.source === id && edge.sourceHandle === `right-buttonId-${buttonId}`
    );
  };

  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: 2,
        position: "relative",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        width: 280,
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          backgroundColor: data.isValid === false ? "#FF7171" : bgColors.green,
          color: "white",
          padding: "8px 12px",
          borderTopLeftRadius: "6px",
          borderTopRightRadius: "6px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h6">{selectedAction?.label}</Typography>
        <Box display="flex" flexDirection="row">
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleEdit}
          >
            <EditOutlinedIcon />
          </IconButton>
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleDelete}
          >
            <DeleteOutlinedIcon />
          </IconButton>
        </Box>
        <Box sx={{ position: "absolute", top: 100, left: -2 }}>
          <CustomHandle
            type="target"
            position={Position.Left}
            id={`left-nodeId-${id}`}
          />
        </Box>
        <Box sx={{ position: "absolute", top: 100, right: -2 }}>
          <CustomHandle
            type="source"
            position={Position.Right}
            id={`right-nodeId-${id}`}
          />
        </Box>
      </Box>
      <Box p={2}>
        {data.isValid === false && (
          <Box
            sx={{
              backgroundColor: "#FFEEEE",
              p: 1,
              mb: 2,
              borderRadius: 1,
              border: "1px solid #FF7171",
            }}
          >
            <Box component="ul" sx={{ m: 0, pl: 2 }}>
              {!message && (
                <Typography component="li" variant="caption" color="error">
                  Message cannot be empty
                </Typography>
              )}
              {data.variables?.customMessageVariables?.length > 0 &&
                data.variables?.customMessageVariables?.some(
                  (variable: any) => !variable.value
                ) && (
                  <Typography component="li" variant="caption" color="error">
                    variable value cannot be empty
                  </Typography>
                )}
              {data.variables?.customMessageVariables?.length > 0 &&
                data.variables?.customMessageVariables?.some(
                  (variable: any) => !variable.fallbackValue
                ) && (
                  <Typography component="li" variant="caption" color="error">
                    variable fallback value cannot be empty
                  </Typography>
                )}
              {data.selectedButtonValue === InteractiveType.Button &&
                data.buttons?.some((button: any) => !button.text) && (
                  <Typography component="li" variant="caption" color="error">
                    Button text cannot be empty
                  </Typography>
                )}
              {data.selectedButtonValue === InteractiveType.List &&
                data.selectedList?.inputs?.some((item: any) => !item.title) && (
                  <Typography component="li" variant="caption" color="error">
                    List item title cannot be empty
                  </Typography>
                )}
              {data.selectedButtonValue === InteractiveType.Button &&
                data.buttons?.length > 0 &&
                (!data.buttons.some((button: any) =>
                  isButtonConnected(button.id, edges)
                ) ||
                  !isNodeConnected(id, edges)) && (
                  <Typography component="li" variant="caption" color="error">
                    Either connect all buttons to other nodes or connect this
                    node directly
                  </Typography>
                )}
              {data.selectedButtonValue === InteractiveType.List &&
                data.selectedList?.inputs?.length > 0 &&
                !isNodeConnected(id, edges) && (
                  <Typography component="li" variant="caption" color="error">
                    Either connect all list items to other nodes or connect this
                    node directly
                  </Typography>
                )}
            </Box>
          </Box>
        )}
        <Typography
          sx={{
            background: bgColors.green1,
            wordBreak: "break-word",
            whiteSpace: "pre-wrap",
            p: 2,
            borderRadius: 2,
          }}
        >
          <div
            dangerouslySetInnerHTML={{
              __html: parseTextToHtml(
                formatContentForDisplay(message) || "Message will display here"
              ),
            }}
          />
        </Typography>
        {selectedButtonValue === InteractiveType.Button &&
          buttons?.map(({ text, id }: any) =>
            text ? (
              <Button
                key={id}
                variant="outlined"
                fullWidth
                sx={{
                  background: bgColors.green1,
                  borderRadius: 2,
                  mt: 1,
                  position: "relative",
                  color: bgColors.green,
                  "& .react-flow__handle": {
                    zIndex: 1002,
                  },
                }}
              >
                {text}
                <Box
                  sx={{
                    position: "absolute",
                    right: -2,
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <CustomHandle
                    type="source"
                    position={Position.Right}
                    id={`right-buttonId-${id}`}
                  />
                </Box>
              </Button>
            ) : (
              <Box
                key={id}
                mt={1}
                sx={{ background: bgColors.green1, borderRadius: 2 }}
                p={1}
                textAlign={"center"}
              >
                Button Text will appear here
              </Box>
            )
          )}

        {selectedButtonValue === InteractiveType.List && selectedList && (
          <Box>
            {selectedList?.inputs?.map((input: any, index: number) =>
              input ? (
                <Button
                  key={input.id}
                  variant="outlined"
                  fullWidth
                  sx={{
                    background: bgColors.green1,
                    borderRadius: 2,
                    mt: 1,
                    position: "relative",
                    color: bgColors.green,
                    "& .react-flow__handle": {
                      zIndex: 1002,
                    },
                  }}
                >
                  {input.title}
                  <Box
                    sx={{
                      position: "absolute",
                      right: -2,
                      height: "100%",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <CustomHandle
                      type="source"
                      position={Position.Right}
                      id={`right-listId-${input.id}`}
                    />
                  </Box>
                </Button>
              ) : (
                <Box
                  key={index}
                  mt={1}
                  sx={{ background: bgColors.green1, borderRadius: 2 }}
                  p={1}
                  textAlign={"center"}
                >
                  List will appear here
                </Box>
              )
            )}
          </Box>
        )}
      </Box>

      {/* Right Sidebar - This is not part of the draggable area */}
      {isMessagePanelOpen && (
        <MessageEditorPanel
          handleMessage={handleMessage}
          selectedButtonValue={selectedButtonValue}
          handleSelectedButtonValue={handleSelectedButtonValue}
          buttons={buttons}
          handleButtons={handleButtons}
          updateNodeData={updateNodeData}
          selectedList={selectedList}
          handleSelectedList={handleSelectedList}
          handleSidebar={handleEdit}
          message={message}
          selectedRadioType={selectedRadioType}
          handleRadioType={handleRadioType}
          handleButtonText={handleButtonText}
          handleRemoveButton={handleRemoveButton}
          selectedAction={selectedAction}
          variables={localVariables}
          handleVariablesChange={handleVariablesChange}
          selectedVariable={selectedVariable}
          handleSelectedVariable={handleSelectedVariable}
          showSaveUserResponse={showSaveUserResponse}
          handleShowSaveUserResponse={handleShowSaveUserResponse}
          saveResponseType={saveResponseType}
          handleSaveResponseType={handleSaveResponseType}
          response={response}
          selectedUserResponse={selectedUserResponse}
          editSelectResponseId={editSelectResponseId}
          handleSelectedResponse={handleSelectedResponse}
          handleResponse={handleResponse}
          handleEditSelectResponseId={handleEditSelectResponseId}
          deleteSelectResponseId={deleteSelectResponseId}
          handleDeleteSelectResponseId={handleDeleteSelectResponseId}
          handleVariablesValueChange={handleVariablesValueChange}
          handleVariablesFallbackValueChange={
            handleVariablesFallbackValueChange
          }
          workflowNodeId={id}
        />
      )}
    </Paper>
  );
};

export default SendMessageNode;
