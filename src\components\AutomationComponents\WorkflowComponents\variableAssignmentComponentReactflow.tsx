import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  FormControlLabel,
  IconButton,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import DeleteIconSvg from "../../../assets/svgs/DeleteIconSvg";
import { variableOptions } from "./functions";
import TextFieldWithBorderComponent from "../../common/TextFieldWithBorderComponent";
import { onChange } from "react-toastify/dist/core/store";

const VariableAssignmentComponentReactflow = ({
  variables,
  variableType,
  handleVariablesChange,
  handleRemoveVariable,
  onVariablesValueChange,
  onVariableFallbackValueChange,
  attributesData,
}: any) => {
  const [expanded, setExpanded] = useState(false);
  const [localVariables, setLocalVariables] = useState(variables);
  const [errors, setErrors] = useState<
    { value: boolean; fallbackValue: boolean }[]
  >([]);
  const [touched, setTouched] = useState<{
    value: boolean[];
    fallbackValue: boolean[];
  }>({
    value: [],
    fallbackValue: [],
  });

  useEffect(() => {
    setLocalVariables(variables);
    // Initialize errors array
    const variablesToCheck = variables?.customMessageVariables?.length
      ? variables?.customMessageVariables
      : variables?.webhookBodyVariables;
    setErrors(
      variablesToCheck?.map((v: any) => ({
        value: !v.value,
        fallbackValue: !v.fallbackValue,
      })) || []
    );
  }, [variables]);

  const variablesToCheck = localVariables?.customMessageVariables?.length
    ? localVariables?.customMessageVariables
    : localVariables?.webhookBodyVariables;

  const handleAccordionChange = (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpanded(isExpanded);
  };

  const handleVariableTypeChange = (index: number, newType: string) => {
    const currentVariables = localVariables?.webhookBodyVariables;

    const updatedVariables = currentVariables?.map((v: any, i: number) =>
      i === index
        ? { ...v, referenceTableType: newType === "variable" ? 0 : 1 }
        : v
    );

    const newVariables = {
      ...localVariables,
      webhookBodyVariables: updatedVariables,
    };

    handleVariablesChange(updatedVariables);
  };

  const handleVariableValueChange = (index: number, newValue: string) => {
    const updatedVariable = {
      ...(variablesToCheck?.[index] || {}),
      value: newValue || "",
    };
    // Update error state
    setErrors((prev) => {
      const copy = [...prev];
      copy[index] = { ...copy[index], value: !newValue };
      return copy;
    });
    if (newValue) {
      setTouched((prev) => {
        const copy = { ...prev };
        copy.value[index] = true;
        return copy;
      });
    }
    onVariablesValueChange(updatedVariable);
  };

  const handleFallbackValueChange = (index: number, newValue: string) => {
    const updatedVariable = {
      ...(variablesToCheck?.[index] || {}),
      fallbackValue: newValue || "",
    };
    setErrors((prev) => {
      const copy = [...prev];
      copy[index] = { ...copy[index], fallbackValue: !newValue };
      return copy;
    });
    if (newValue) {
      setTouched((prev) => {
        const copy = { ...prev };
        copy.fallbackValue[index] = true;
        return copy;
      });
    }
    onVariableFallbackValueChange(updatedVariable);
  };

  const getVariableCount = () => {
    const filtered = variablesToCheck?.filter((item: any) => {
      return item.type === variableType;
    });

    return filtered?.length || 0;
  };

  return (
    <Accordion expanded={expanded} onChange={handleAccordionChange}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography
          sx={{
            fontWeight: 500,
            fontSize: "14px",
            display: "flex",
            alignItems: "center",
          }}
        >
          Variables
        </Typography>
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: "14px",
            color: "primary.main",
            marginLeft: 1,
            backgroundColor: "rgba(0, 0, 0, 0.1)",
            borderRadius: "20px",
            padding: "0 7px",
          }}
        >
          {getVariableCount()}
        </Typography>
      </AccordionSummary>

      <AccordionDetails sx={{ height: "200px", overflowY: "auto" }}>
        {variablesToCheck
          ?.filter((item: any) => item?.type === variableType)
          ?.map((variable: any, index: number) => (
            <Box
              key={index}
              sx={{
                mb: 2,
                border: "1px solid #e0e0e0",
                padding: 1,
                borderRadius: 1,
                position: "relative",
              }}
            >
              <IconButton
                onClick={(event) => {
                  event.stopPropagation();
                  handleRemoveVariable(index);
                }}
                size="small"
                sx={{
                  position: "absolute",
                  top: 8,
                  right: 8,
                  color: "red",
                }}
              >
                <DeleteIconSvg />
              </IconButton>

              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                Variable {variable.veriable}
              </Typography>

              <RadioGroup
                value="variable"
                onChange={(e) =>
                  handleVariableTypeChange(index, e.target.value)
                }
                sx={{ mb: 1 }}
              >
                {variableOptions?.map((option) => (
                  <FormControlLabel
                    key={option.value}
                    value={option.value}
                    control={
                      <Radio
                        sx={{
                          "&.Mui-checked": { color: "green" },
                          "& .MuiSvgIcon-root": { fontSize: 14 },
                        }}
                      />
                    }
                    label={option.label}
                    sx={{
                      "& .MuiFormControlLabel-label": {
                        fontSize: "14px",
                      },
                    }}
                  />
                ))}
              </RadioGroup>

              <Select
                fullWidth
                value={variable.value}
                onChange={(e) => {
                  handleVariableValueChange(index, e.target.value);
                }}
                displayEmpty
                sx={{
                  mb: 1,
                  height: "36px",
                  fontSize: 14,
                  borderRadius: "10px",
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "10px",
                  },
                }}
                size="small"
                error={errors[index]?.value && touched.value[index]}
                MenuProps={{
                  PaperProps: {
                    style: {
                      maxHeight: 200,
                      overflowY: "auto",
                    },
                  },
                }}
                onBlur={() =>
                  setTouched((prev) => {
                    const copy = { ...prev };
                    copy.value[index] = true;
                    return copy;
                  })
                }
              >
                <MenuItem disabled value="">
                  <Typography style={{ color: "#a8a7a7", fontSize: "14px" }}>
                    Select a variable
                  </Typography>
                </MenuItem>
                {attributesData?.map((variable: any) => {
                  return (
                    <MenuItem
                      sx={{ fontSize: 14 }}
                      key={variable.id}
                      value={variable.name}
                    >
                      {variable.name}
                    </MenuItem>
                  );
                })}
              </Select>
              {errors[index]?.value && touched.value[index] && (
                <Typography color="error" sx={{ fontSize: 12, mb: 1 }}>
                  Value is required
                </Typography>
              )}

              <TextFieldWithBorderComponent
                fullWidth
                placeholder=""
                name="fallBackValue"
                label="Enter fallback value"
                value={variable.fallbackValue}
                onChange={(e) =>
                  handleFallbackValueChange(index, e.target.value)
                }
                size="small"
                sx={{
                  height: "36px",
                  fontSize: "14px",
                  borderRadius: "10px",
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "10px",
                  },
                }}
                error={
                  errors[index]?.fallbackValue && touched.fallbackValue[index]
                }
                helperText={
                  errors[index]?.fallbackValue && touched.fallbackValue[index]
                    ? "Fallback value is required"
                    : ""
                }
                onBlur={() =>
                  setTouched((prev) => {
                    const copy = { ...prev };
                    copy.fallbackValue[index] = true;
                    return copy;
                  })
                }
              />
            </Box>
          ))}
      </AccordionDetails>
    </Accordion>
  );
};

export default VariableAssignmentComponentReactflow;
