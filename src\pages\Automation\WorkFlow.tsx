import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Card,
  Switch,
  FormControlLabel,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../utils/bgColors";

import {
  RootState,
  useAppDispatch,
  useAppSelector,
} from "../../utils/redux-hooks";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
} from "@mui/icons-material";
import { checkWorkflowsPermission } from "../../utils/permissions";
import NoAccessPage from "../../components/common/NoAccess";
import { toastActions } from "../../utils/toastSlice";
import LoadingComponent from "../../components/common/LoadingComponent";
import { deleteWorkflow } from "../../redux/slices/Workflows/deleteWorkflowSlice";
import { getWorkflowNames } from "../../redux/slices/Workflows/getWorkflowNamesSlice";
import DeletePopUp from "../../components/common/DeletePopup";
import { getWorkflow } from "../../redux/slices/Workflows/getWorkflowSlice";
import { LuWorkflow } from "react-icons/lu";
import CreateWorkflowDialog from "./CreateWorkflowDialog";
import CommonTable from "../../components/common/CommonTable";
import { getAllWorkflowsReactflow } from "../../redux/slices/Workflows/getAllWorkflowsReactflowSlice";
import { deleteWorkflowReactflow } from "../../redux/slices/Workflows/deleteWorkflowReactflowSlice";
import { updateWorkflowReactflow } from "../../redux/slices/Workflows/updateWorkflowReactflowSlice";

import { useNavigate } from "react-router-dom";
import { toggleWorkflowActive } from "../../redux/slices/Workflows/toggleWorkflowActiveSlice";
import { fontSize } from "@mui/system";
import { color } from "echarts";

// Custom formatDate function instead of importing from date-fns
const formatDate = (datetime: any) => {
  if (!datetime) return "N/A";

  try {
    const date = new Date(datetime);
    if (isNaN(date.getTime())) return "N/A";

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${day}-${month}-${year}`;
  } catch (error) {
    return "N/A";
  }
};

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white,
    width: "100%",
    height: "100%",
  },
  chatArea: {
    padding: "20px",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
    height: "100%",
    width: "100%",
  },
  manageContainer: {
    display: "flex",

    justifyContent: "space-between",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "600 !important",
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "140px",
    height: "32px",
    cursor: "pointer",
  },
  textColor: {
    color: bgColors.gray1,
    fontSize: "16px",
  },
  templateBox: {
    backgroundColor: "#f5f5f5",
    borderRadius: "10px",
    padding: "20px",
    marginTop: "20px",
    borderLeft: `4px solid ${bgColors.green}`,
  },
  headerText: {
    fontWeight: "600 !important",
    marginBottom: "10px !important",
    fontSize: "15px",
  },
  tableContainer: {
    marginTop: "10px",
    boxShadow: "none",
    backgroundColor: "transparent",
  },
  table: {
    // borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      borderBottom: "1px solid #f0f0f0",
      padding: "4px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
      // paddingLeft:'4px'
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  tableRow: {
    transition: "box-shadow 0.3s ease",
    "&:hover": {
      boxShadow:
        "0px 3px 5px -1px rgba(0,0,0,0.2), 0px 6px 10px 0px rgba(0,0,0,0.14), 0px 1px 18px 0px rgba(0,0,0,0.12)",
    },
  },
  grayText: {
    color: bgColors.gray1,
  },
  workflowsCreatedBox: {
    marginTop: "30px",
  },
  workflowItem: {
    backgroundColor: "#fff",
    borderRadius: "5px",
    padding: "10px",
    marginBottom: "10px",
  },
  overlayLoader: {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 9999,
  },
});

const WorkFlow = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [workflowData, setWorkflowData] = useState<any>(null);

  const [isWorkflowEditing, setIsWorkflowEditing] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [workflowToDelete, setWorkflowToDelete] = useState<any>(null);
  const [isWorkflowDeleteLoading, setIsWorkflowDeleteLoading] = useState(false);
  const [isOpenCreateWorkflowDialog, setOpenCreateWorkflowDialog] =
    useState(false);

  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;
  const { workflowNamesData, workflowNamesStatus } = useAppSelector(
    (state: any) => state.getWorkflowNames
  );

  const { getAllWorkflowsReactflowData, getAllWorkflowsReactflowStatus } =
    useAppSelector((state: any) => state.getAllWorkflowsReactflow);

  const { getWorkflowReactflowByIdData, getWorkflowReactflowByIdStatus } =
    useAppSelector((state: any) => state.getWorkflowReactflowById);

  const getUserPermissionSlice = useAppSelector(
    (state: any) => state.getUserPermissions
  );
  const getUserPermissionData = getUserPermissionSlice?.data;
  const hasWorkflowsPermission = checkWorkflowsPermission(
    getUserPermissionData?.automation
  );

  const handleWorkflowStatusToggle = async (
    workflowId: string,
    currentStatus: boolean
  ) => {
    try {
      const workflow = workflowData.find((w: any) => w.id === workflowId);
      if (!workflow) return;

      const payload = {
        workflowId: workflowId,
      };
      const response = await dispatch(toggleWorkflowActive(payload));

      if (response?.meta?.requestStatus === "fulfilled") {
        currentStatus = response?.payload?.data;
        setWorkflowData((prevData: any) =>
          prevData.map((item: any) =>
            item.id === workflowId ? { ...item, isActive: currentStatus } : item
          )
        );
        // dispatch(
        //   toastActions.setToaster({
        //     message: `Workflow ${currentStatus ? 'activated' : 'deactivated'} successfully`,
        //     type: "success",
        //   })
        // );
      } else {
        dispatch(
          toastActions.setToaster({
            message:
              response?.payload?.message ||
              "Failed to activate/deactivate workflow",
            type: "error",
          })
        );
      }
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          message:
            error.message ||
            "An error occurred while activating/deactivating workflow",
          type: "error",
        })
      );
    }
  };

  const handleWorkflow = async (workflow: any) => {
    setIsWorkflowEditing(true);
    navigate(`/automation/workflows/${workflow?.id}`);
  };
  const handleDeleteClick = (workflow: any) => {
    setWorkflowToDelete(workflow);
    setIsDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (workflowToDelete) {
      setIsWorkflowDeleteLoading(true);
      try {
        const payload = {
          companyId: userData?.companyId,
          WorkflowId: workflowToDelete.id,
        };
        const result = await dispatch(deleteWorkflowReactflow(payload));
        setIsWorkflowDeleteLoading(false);
        if (result?.meta?.requestStatus === "fulfilled") {
          dispatch(
            toastActions.setToaster({
              message:
                result?.payload?.message || "Workflow deleted successfully",
              type: "success",
            })
          );
          // Refresh the workflow list
          dispatch(getAllWorkflowsReactflow(userData?.companyId));
        } else {
          dispatch(
            toastActions.setToaster({
              message: result?.payload?.message || "Failed to delete workflow",
              type: "error",
            })
          );
          setIsWorkflowDeleteLoading(false);
        }
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            message:
              error.message || "An error occurred while deleting the workflow",
            type: "error",
          })
        );
        setIsWorkflowDeleteLoading(false);
      }
    }
    setIsDeleteConfirmOpen(false);
    setWorkflowToDelete(null);
  };
  const handleCreateWorkflowDialog = () => {
    setOpenCreateWorkflowDialog(true);
  };

  const handleDeleteCancel = () => {
    setIsDeleteConfirmOpen(false);
    setWorkflowToDelete(null);
    dispatch(getAllWorkflowsReactflow(userData?.companyId));
  };

  const columns = [
    { id: "workflowName", label: "Workflow Name" },
    { id: "status", label: "Status" },
    { id: "updatedAt", label: "Last Updated" },
  ];

  const renderStatusToggle = (row: any) => (
    <FormControlLabel
      control={
        <Switch
          checked={row.originalData.isActive}
          onChange={() =>
            handleWorkflowStatusToggle(row.id, row.originalData.isActive)
          }
          sx={{
            "& .MuiSwitch-switchBase.Mui-checked": {
              color: "#4caf50",
              "&:hover": {
                backgroundColor: "rgba(76, 175, 80, 0.04)",
              },
            },
            "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
              backgroundColor: "#4caf50",
            },
          }}
          size="small"
        />
      }
      label={
        <Typography variant="caption">
          {/* {row.originalData.isActive ? "Active" : "Inactive"} */}
        </Typography>
      }
      labelPlacement="end"
      sx={{ margin: 0 }}
    />
  );

  const renderActions = (row: any) => (
    <Box
      sx={{
        display: "flex",
        gap: 0.5,
        justifyContent: "flex-start",
        alignItems: "center",
        pl: 0,
        pr: 0,
      }}
    >
      <IconButton
        size="small"
        sx={{ p: 0 }}
        onClick={() => {
          handleWorkflow(row);
        }}
      >
        <EditIcon fontSize="small" />
      </IconButton>
      <IconButton
        onClick={() => handleDeleteClick(row)}
        size="small"
        sx={{ p: 0 }}
      >
        <DeleteIcon fontSize="small" />
      </IconButton>
    </Box>
  );

  const renderOnMobile = (row: any) => (
    <Card sx={{ padding: 2, mb: 2 }}>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Typography variant="subtitle1" fontWeight="500">
                {row.workflowName}
              </Typography>

              <Box sx={{ display: "flex", gap: 0.25 }}>
                <IconButton
                  onClick={() => handleWorkflow(row)}
                  size="small"
                  sx={{
                    color: "#666",
                    "&:hover": { color: "#3b82f6" },
                    padding: "2px",
                    "& .MuiSvgIcon-root": {
                      fontSize: "1.1rem",
                    },
                  }}
                >
                  <EditIcon />
                </IconButton>
                <IconButton
                  onClick={() => handleDeleteClick(row)}
                  size="small"
                  sx={{
                    color: "#666",
                    "&:hover": { color: "#ef4444" },
                    padding: "2px",
                    "& .MuiSvgIcon-root": {
                      fontSize: "1.1rem",
                    },
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Box>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <Typography
                variant="caption"
                sx={{
                  backgroundColor: "#f5f5f5",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  color: "#666",
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                Status:
                <Switch
                  checked={row.originalData.isActive}
                  onChange={() =>
                    handleWorkflowStatusToggle(
                      row.id,
                      row.originalData.isActive
                    )
                  }
                  sx={{
                    "& .MuiSwitch-switchBase.Mui-checked": {
                      color: "#4caf50",
                      "&:hover": {
                        backgroundColor: "rgba(76, 175, 80, 0.04)",
                      },
                    },
                    "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                      backgroundColor: "#4caf50",
                    },
                  }}
                  size="small"
                />
                {row.originalData.isActive ? "Active" : "Inactive"}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  backgroundColor: "#f5f5f5",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  color: "#666",
                }}
              >
                Last Updated:{" "}
                {row.updatedAt ? formatDate(row.updatedAt) : "N/A"}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Card>
  );

  const transformedData = workflowData?.map((workflow: any) => ({
    id: workflow.id,
    workflowName: workflow.name,
    status: renderStatusToggle({ id: workflow.id, originalData: workflow }),
    updatedAt: workflow.updatedAt ? formatDate(workflow.updatedAt) : "N/A",
    originalData: workflow,
  }));

  useEffect(() => {
    if (userData?.companyId) {
      dispatch(getAllWorkflowsReactflow(userData.companyId));
    }
  }, [dispatch, userData?.companyId]);

  useEffect(() => {
    setWorkflowData(getAllWorkflowsReactflowData?.data || null);
  }, [getAllWorkflowsReactflowData]);

  return (
    <>
      {hasWorkflowsPermission ? (
        <Box style={{ position: "relative" }}>
          {getAllWorkflowsReactflowStatus === "loading" && (
            <Box className={classes.overlayLoader}>
              <LoadingComponent height="50px" color={bgColors.blue} />
            </Box>
          )}
          <Grid container className={classes.mainContainer}>
            <Grid item className={classes.bgContainer}>
              <CommonTable
                columns={columns}
                actions={renderActions}
                renderOnMobile={renderOnMobile}
                data={transformedData || []}
                rowIdKey="id"
                showPagination={false}
                title="Workflows"
                count={workflowData?.length}
                primaryAction={{
                  label: "Add Workflow",
                  onClick: handleCreateWorkflowDialog,
                  disabled: getAllWorkflowsReactflowStatus === "loading",
                }}
                optionalTypographyInRenderToolbar="This section allows users to create and manage Custom Auto
                Replies in response to customer chats."
              />
              <DeletePopUp
                open={isDeleteConfirmOpen}
                handleDelete={handleDeleteConfirm}
                handleClose={handleDeleteCancel}
                title={`Workflow "${workflowToDelete?.workflowName}"`}
                handleLoad={isWorkflowDeleteLoading}
              />
            </Grid>

            <CreateWorkflowDialog
              setOpen={setOpenCreateWorkflowDialog}
              open={isOpenCreateWorkflowDialog}
              initialWorkflowData={workflowData}
              isWorkflowEditing={isWorkflowEditing}
              setIsWorkflowEditing={setIsWorkflowEditing}
            />
          </Grid>
        </Box>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default WorkFlow;
