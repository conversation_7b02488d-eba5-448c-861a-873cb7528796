import { Box, IconButton, Paper, Typography } from "@mui/material";
import { useState, useEffect } from "react";
import CustomResponseComp from "./CustomResponseComp";
import { bgColors } from "../../../utils/bgColors";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import { ContentState, convertToRaw, EditorState } from "draft-js";
import { formatContent } from "../../../utils/functions";
import { InteractiveType } from "./enums";
import { v4 as uuidv4 } from "uuid";

const MessageEditorPanel = ({
  handleMessage,
  selectedButtonValue,
  handleSelectedButtonValue,
  buttons,
  handleButtons,
  selectedList,
  handleSelectedList,
  handleSidebar,
  message,
  handleButtonText,
  handleRemoveButton,
  selectedAction,
  variables,
  handleVariablesChange,
  selectedVariable,
  handleSelectedVariable,
  handleShowSaveUserResponse,
  showSaveUserResponse,
  saveResponseType,
  handleSaveResponseType,
  handleVariablesValueChange,
  handleVariablesFallbackValueChange,
  updateNodeData,
  workflowNodeId,
  selectedUserResponse,
}: any) => {
  const [editorState, setEditorState] = useState(() =>
    typeof message === "string"
      ? EditorState.createWithContent(ContentState.createFromText(message))
      : EditorState.createEmpty()
  );

  useEffect(() => {
    if (
      selectedButtonValue === InteractiveType.Button &&
      (!buttons || buttons.length === 0)
    ) {
      handleButtons({
        id: uuidv4(),
        text: "",
      });
    }
  }, [selectedButtonValue, buttons, handleButtons]);

  const handleEditorStateChange = (newEditorOrTextState: any, type: string) => {
    if (type === "editor") {
      const formattedContent = formatContent(
        newEditorOrTextState?.getCurrentContent()
      );
      handleMessage(formattedContent);
    }
  };

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -360,
        top: 0,
        width: 350,
        height: 460,
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s ease",
        zIndex: 10,
      }}
      className="nowheel nopan nodrag"
    >
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
          height: "50px",
        }}
      >
        <Typography variant="h6" color="white">
          Message Editor
        </Typography>
        <IconButton
          aria-label="close"
          sx={{
            color: `${bgColors.red}`,
            width: 48,
            height: 48,
            "& svg": {
              width: 40,
              height: 40,
              fill: `${bgColors.red1}`,
            },
          }}
          onClick={() => handleSidebar(false)}
        >
          <CloseIconSvg />
        </IconButton>
      </Box>
      <Box
        sx={{
          flexGrow: 1,
          overflowY: "auto",
          padding: 2,
          height: "calc(460px - 50px)",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <CustomResponseComp
          selectedButtonValue={selectedButtonValue}
          handleSelectedButtonValue={handleSelectedButtonValue}
          buttons={buttons}
          handleButtons={handleButtons}
          selectedList={selectedList}
          handleSelectedList={handleSelectedList}
          message={message}
          handleMessage={handleMessage}
          handleButtonText={handleButtonText}
          handleRemoveButton={handleRemoveButton}
          selectedAction={selectedAction}
          variables={variables}
          handleVariablesChange={handleVariablesChange}
          selectedVariable={selectedVariable}
          handleSelectedVariable={handleSelectedVariable}
          showSaveUserResponse={showSaveUserResponse}
          handleShowSaveUserResponse={handleShowSaveUserResponse}
          saveResponseType={saveResponseType}
          handleSaveResponseType={handleSaveResponseType}
          handleEditorStateChange={handleEditorStateChange}
          editorState={editorState}
          setEditorState={setEditorState}
          handleVariablesValueChange={handleVariablesValueChange}
          handleVariablesFallbackValueChange={
            handleVariablesFallbackValueChange
          }
          updateNodeData={updateNodeData}
          workflowNodeId={workflowNodeId}
          selectedUserResponse={selectedUserResponse}
        />
      </Box>
    </Paper>
  );
};

export default MessageEditorPanel;
