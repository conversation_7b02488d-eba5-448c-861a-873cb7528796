import { Box, IconButton, Paper, Typography } from "@mui/material";
import { useState, useEffect, useCallback, useRef } from "react";
import CustomResponseComp from "./CustomResponseComp";
import { bgColors } from "../../../utils/bgColors";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import {
  ContentState,
  convertToRaw,
  EditorState,
  SelectionState,
  Modifier,
} from "draft-js";
import { formatContent } from "../../../utils/functions";
import { InteractiveType } from "./enums";
import { v4 as uuidv4 } from "uuid";
import { extractVariables, replaceVariablesSequentially } from "./functions";
import { debounce } from "lodash";
import { toast } from "react-toastify";
import { useAppDispatch } from "../../../utils/redux-hooks";
import { toastActions } from "../../../utils/toastSlice";

const MessageEditorPanel = ({
  handleMessage,
  selectedButtonValue,
  handleSelectedButtonValue,
  buttons,
  handleButtons,
  selectedList,
  handleSelectedList,
  handleSidebar,
  message,
  handleButtonText,
  handleRemoveButton,
  selectedAction,
  variables,
  handleVariablesChange,
  selectedVariable,
  handleSelectedVariable,
  handleShowSaveUserResponse,
  showSaveUserResponse,
  saveResponseType,
  handleSaveResponseType,
  handleVariablesValueChange,
  handleVariablesFallbackValueChange,
  updateNodeData,
  workflowNodeId,
  selectedUserResponse,
}: any) => {
  const [editorState, setEditorState] = useState(() => {
    try {
      if (typeof message === "string" && message) {
        const contentState = ContentState.createFromText(message);
        return EditorState.createWithContent(contentState);
      }
      return EditorState.createEmpty();
    } catch (error) {
      console.error("Error initializing editor state:", error);
      return EditorState.createEmpty();
    }
  });

  const [selectedVariableType, setSelectedVariableType] = useState<
    "addVariable" | "leadratVariable" | null
  >(null);

  const lastSelectionRef = useRef<SelectionState | null>(null);

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (
      selectedButtonValue === InteractiveType.Button &&
      (!buttons || buttons.length === 0)
    ) {
      handleButtons({
        id: uuidv4(),
        text: "",
      });
    }
  }, [selectedButtonValue, buttons, handleButtons]);

  // Create a debounced version of the state change handler
  const debouncedStateUpdate = useCallback(
    debounce(
      (
        formattedContent: string,
        normalVariables: any[],
        predefinedVariables: any[]
      ) => {
        // Update message content if changed
        if (formattedContent !== message) {
          handleMessage(formattedContent);
        }

        // Compare normal variables
        const existingNormalVars =
          variables?.customMessageVariables?.[0]?.normalVariables || [];
        const normalChanged =
          JSON.stringify(normalVariables) !==
          JSON.stringify(existingNormalVars);

        // Compare predefined variables
        const existingPredefinedVars =
          variables?.customMessageVariables?.[0]?.leadratVariables || [];
        const predefinedChanged =
          JSON.stringify(predefinedVariables) !==
          JSON.stringify(existingPredefinedVars);

        // Handle variable changes
        if (normalChanged) {
          handleVariablesChange(normalVariables, "addVariable");
        }

        if (predefinedChanged) {
          handleVariablesChange(predefinedVariables, "leadratVariable");
        }
      },
      300
    ),
    [message, variables, handleMessage, handleVariablesChange]
  );

  const handleEditorStateChange = useCallback(
    (newEditorOrTextState: any, type: string) => {
      if (type === "editor" && newEditorOrTextState) {
        try {
          // Store the current selection before any changes
          const currentSelection = newEditorOrTextState.getSelection();
          lastSelectionRef.current = currentSelection;

          const currentContent = newEditorOrTextState.getCurrentContent();
          if (!currentContent) return;

          const formattedContent = formatContent(currentContent);

          // Only proceed if content changed
          if (formattedContent === message) return;

          // Use the current selectedVariableType state
          const currentVariableType = selectedVariableType;

          const { normalVariables, predefinedVariables, cleanedContent } =
            extractVariables(
              newEditorOrTextState,
              variables?.customMessageVariables?.[0]?.normalVariables || [],
              variables?.predefinedMessageVariables || [],
              currentVariableType,
              (message) =>
                dispatch(toastActions.setToaster({ type: "error", message }))
            );

          // Update selectedVariableType based on the variables found
          if (normalVariables.length > 0 && predefinedVariables.length === 0) {
            setSelectedVariableType("addVariable");
          } else if (
            normalVariables.length === 0 &&
            predefinedVariables.length > 0
          ) {
            setSelectedVariableType("leadratVariable");
          }

          // Only renumber content if normal variables are present
          let updatedContent = cleanedContent;
          if (normalVariables.length > 0) {
            updatedContent = replaceVariablesSequentially(cleanedContent);
          }

          // Create new content state with updated content
          const newContentState = ContentState.createFromText(updatedContent);
          let newEditorState = EditorState.createWithContent(newContentState);

          // If we have a stored selection, try to restore it
          if (lastSelectionRef.current) {
            const selection = lastSelectionRef.current;
            const blockMap = newContentState.getBlockMap();
            const block = blockMap.first();

            if (block) {
              const blockKey = block.getKey();
              const blockLength = block.getLength();
              const offset = Math.min(selection.getStartOffset(), blockLength);

              const newSelection = SelectionState.createEmpty(blockKey).merge({
                anchorOffset: offset,
                focusOffset: offset,
              });

              newEditorState = EditorState.forceSelection(
                newEditorState,
                newSelection
              );
            }
          }

          // Update local state immediately
          setEditorState(newEditorState);

          // Debounce the heavy updates
          debouncedStateUpdate(
            updatedContent,
            normalVariables,
            predefinedVariables
          );
        } catch (error) {
          console.error("Error in handleEditorStateChange:", error);
        }
      }
    },
    [message, variables, selectedVariableType, debouncedStateUpdate, dispatch]
  );

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedStateUpdate.cancel();
    };
  }, [debouncedStateUpdate]);

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -360,
        top: 0,
        width: 350,
        height: 460,
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s ease",
        zIndex: 10,
      }}
      className="nowheel nopan nodrag"
    >
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
          height: "50px",
        }}
      >
        <Typography variant="h6" color="white">
          Message Editor
        </Typography>
        <IconButton
          aria-label="close"
          sx={{
            color: `${bgColors.red}`,
            width: 48,
            height: 48,
            "& svg": {
              width: 40,
              height: 40,
              fill: `${bgColors.red1}`,
            },
          }}
          onClick={() => handleSidebar(false)}
        >
          <CloseIconSvg />
        </IconButton>
      </Box>
      <Box
        sx={{
          flexGrow: 1,
          overflowY: "auto",
          padding: 2,
          height: "calc(460px - 50px)",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <CustomResponseComp
          selectedButtonValue={selectedButtonValue}
          handleSelectedButtonValue={handleSelectedButtonValue}
          buttons={buttons}
          handleButtons={handleButtons}
          selectedList={selectedList}
          handleSelectedList={handleSelectedList}
          message={message}
          handleMessage={handleMessage}
          handleButtonText={handleButtonText}
          handleRemoveButton={handleRemoveButton}
          selectedAction={selectedAction}
          variables={variables}
          handleVariablesChange={handleVariablesChange}
          selectedVariable={selectedVariable}
          handleSelectedVariable={handleSelectedVariable}
          showSaveUserResponse={showSaveUserResponse}
          handleShowSaveUserResponse={handleShowSaveUserResponse}
          saveResponseType={saveResponseType}
          handleSaveResponseType={handleSaveResponseType}
          handleEditorStateChange={handleEditorStateChange}
          editorState={editorState}
          setEditorState={setEditorState}
          handleVariablesValueChange={handleVariablesValueChange}
          handleVariablesFallbackValueChange={
            handleVariablesFallbackValueChange
          }
          updateNodeData={updateNodeData}
          workflowNodeId={workflowNodeId}
          selectedUserResponse={selectedUserResponse}
          selectedVariableType={selectedVariableType}
          setSelectedVariableType={setSelectedVariableType}
        />
      </Box>
    </Paper>
  );
};

export default MessageEditorPanel;
